[{"C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\app\\App.tsx": "2", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\app\\store.ts": "3", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\app\\routes.ts": "4", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\app\\events.ts": "5", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\app\\Layout.tsx": "6", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\customer-service.ts": "7", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\order-service.ts": "8", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\zone-service.ts": "9", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\plant-service.ts": "10", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\auth\\require-auth.tsx": "11", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\detail-slice.ts": "12", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\plants\\plants-slice.ts": "13", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\zones\\zones-slice.ts": "14", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\auth\\auth-provider.tsx": "15", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\auth\\Login.tsx": "16", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\zones\\detail-slice.ts": "17", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\orders-slice.ts": "18", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\plants\\detail-slice.ts": "19", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\customers\\customers-slice.ts": "20", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\customers\\detail-slice.ts": "21", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\users\\users-slice.ts": "22", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\driver-tasks\\driver-task-slice.ts": "23", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\zones\\Detail.tsx": "24", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\users\\Detail.tsx": "25", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\users\\List.tsx": "26", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\driver-tasks\\Detail.tsx": "27", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\driver-tasks\\List.tsx": "28", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\driver-tasks\\New.tsx": "29", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\zones\\List.tsx": "30", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\customers\\Detail.tsx": "31", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\plants\\List.tsx": "32", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\customers\\List.tsx": "33", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\plants\\Detail.tsx": "34", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\ByStickDate.tsx": "35", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\ByPinchDate.tsx": "36", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\ByFlowerDate.tsx": "37", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\Detail.tsx": "38", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\List.tsx": "39", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\service-base.ts": "40", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\app\\NavMenu.tsx": "41", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\BySpaceDate.tsx": "42", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\colours\\Detail.tsx": "43", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\pages\\Index.tsx": "44", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\colours\\List.tsx": "45", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\auth\\auth-context.ts": "46", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\auth\\use-auth.ts": "47", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\reports-service.ts": "48", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\auth-service.ts": "49", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\driver-tasks-service.ts": "50", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\models\\orders.ts": "51", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\models\\plants.ts": "52", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\models\\zones.ts": "53", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\models\\driver-tasks.ts": "54", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\models\\customers.ts": "55", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\sort.ts": "56", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\loading\\Loading.tsx": "57", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\problem-details.ts": "58", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\format.ts": "59", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\equals.ts": "60", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\error\\Error.tsx": "61", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\boot\\index.ts": "62", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\focus.ts": "63", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\class-names.ts": "64", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\weeks.ts": "65", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\notifications-service.ts": "66", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\driver-tasks\\ListFilters.tsx": "67", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\driver-tasks\\List-Date.tsx": "68", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\guid.ts": "69", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\OrderRow.tsx": "70", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\app\\database.ts": "71", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\LabourReport.tsx": "72", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\SalesWeekRow.tsx": "73", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\Variety.tsx": "74", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\boot\\axios.ts": "75", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\api-base.ts": "76", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\auth\\configuration.ts": "77", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\boot\\font-awesome.ts": "78", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\driver-tasks\\List-Item.tsx": "79", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\colours\\colours-slice.ts": "80", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\colours\\detail-slice.ts": "81", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\models\\colour.ts": "82", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\colour-service.ts": "83", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\plants\\List-Item.tsx": "84"}, {"size": 675, "mtime": 1753379942630, "results": "85", "hashOfConfig": "86"}, {"size": 6534, "mtime": 1753383787294, "results": "87", "hashOfConfig": "86"}, {"size": 1194, "mtime": 1753382476769, "results": "88", "hashOfConfig": "86"}, {"size": 3150, "mtime": 1753381281451, "results": "89", "hashOfConfig": "86"}, {"size": 832, "mtime": 1753383706024, "results": "90", "hashOfConfig": "86"}, {"size": 189, "mtime": 1753379942615, "results": "91", "hashOfConfig": "86"}, {"size": 380, "mtime": 1753379942611, "results": "92", "hashOfConfig": "86"}, {"size": 882, "mtime": 1753379942614, "results": "93", "hashOfConfig": "86"}, {"size": 340, "mtime": 1753379942615, "results": "94", "hashOfConfig": "86"}, {"size": 470, "mtime": 1753463240525, "results": "95", "hashOfConfig": "86"}, {"size": 604, "mtime": 1753379942619, "results": "96", "hashOfConfig": "86"}, {"size": 3382, "mtime": 1753379942627, "results": "97", "hashOfConfig": "86"}, {"size": 5756, "mtime": 1753472768461, "results": "98", "hashOfConfig": "86"}, {"size": 694, "mtime": 1753379942630, "results": "99", "hashOfConfig": "86"}, {"size": 1669, "mtime": 1753379942618, "results": "100", "hashOfConfig": "86"}, {"size": 2313, "mtime": 1753379942618, "results": "101", "hashOfConfig": "86"}, {"size": 3177, "mtime": 1753379942630, "results": "102", "hashOfConfig": "86"}, {"size": 7906, "mtime": 1753379942627, "results": "103", "hashOfConfig": "86"}, {"size": 3227, "mtime": 1753379942628, "results": "104", "hashOfConfig": "86"}, {"size": 766, "mtime": 1753379942620, "results": "105", "hashOfConfig": "86"}, {"size": 3384, "mtime": 1753379942620, "results": "106", "hashOfConfig": "86"}, {"size": 1854, "mtime": 1753379942628, "results": "107", "hashOfConfig": "86"}, {"size": 6721, "mtime": 1753379942622, "results": "108", "hashOfConfig": "86"}, {"size": 4788, "mtime": 1753379942630, "results": "109", "hashOfConfig": "86"}, {"size": 16200, "mtime": 1753379942628, "results": "110", "hashOfConfig": "86"}, {"size": 2126, "mtime": 1753379942628, "results": "111", "hashOfConfig": "86"}, {"size": 9857, "mtime": 1753379942620, "results": "112", "hashOfConfig": "86"}, {"size": 4221, "mtime": 1753379942622, "results": "113", "hashOfConfig": "86"}, {"size": 9032, "mtime": 1753379942622, "results": "114", "hashOfConfig": "86"}, {"size": 2229, "mtime": 1753379942630, "results": "115", "hashOfConfig": "86"}, {"size": 4014, "mtime": 1753379942619, "results": "116", "hashOfConfig": "86"}, {"size": 1822, "mtime": 1753472699848, "results": "117", "hashOfConfig": "86"}, {"size": 1870, "mtime": 1753379942619, "results": "118", "hashOfConfig": "86"}, {"size": 26417, "mtime": 1753472896170, "results": "119", "hashOfConfig": "86"}, {"size": 9851, "mtime": 1753379942625, "results": "120", "hashOfConfig": "86"}, {"size": 10007, "mtime": 1753379942623, "results": "121", "hashOfConfig": "86"}, {"size": 10445, "mtime": 1753379942623, "results": "122", "hashOfConfig": "86"}, {"size": 47495, "mtime": 1753379942626, "results": "123", "hashOfConfig": "86"}, {"size": 10641, "mtime": 1753379942626, "results": "124", "hashOfConfig": "86"}, {"size": 3133, "mtime": 1753463229684, "results": "125", "hashOfConfig": "86"}, {"size": 5358, "mtime": 1753382309129, "results": "126", "hashOfConfig": "86"}, {"size": 9798, "mtime": 1753379942625, "results": "127", "hashOfConfig": "86"}, {"size": 4659, "mtime": 1753384126919, "results": "128", "hashOfConfig": "86"}, {"size": 506, "mtime": 1753379942630, "results": "129", "hashOfConfig": "86"}, {"size": 2093, "mtime": 1753383827780, "results": "130", "hashOfConfig": "86"}, {"size": 362, "mtime": 1753379942618, "results": "131", "hashOfConfig": "86"}, {"size": 149, "mtime": 1753379942619, "results": "132", "hashOfConfig": "86"}, {"size": 4200, "mtime": 1753379942614, "results": "133", "hashOfConfig": "86"}, {"size": 2526, "mtime": 1753379942611, "results": "134", "hashOfConfig": "86"}, {"size": 2754, "mtime": 1753379942611, "results": "135", "hashOfConfig": "86"}, {"size": 2779, "mtime": 1753379942613, "results": "136", "hashOfConfig": "86"}, {"size": 1496, "mtime": 1753472579643, "results": "137", "hashOfConfig": "86"}, {"size": 494, "mtime": 1753379942613, "results": "138", "hashOfConfig": "86"}, {"size": 1320, "mtime": 1753379942612, "results": "139", "hashOfConfig": "86"}, {"size": 635, "mtime": 1753379942612, "results": "140", "hashOfConfig": "86"}, {"size": 1276, "mtime": 1753379942633, "results": "141", "hashOfConfig": "86"}, {"size": 221, "mtime": 1753379942623, "results": "142", "hashOfConfig": "86"}, {"size": 591, "mtime": 1753379942633, "results": "143", "hashOfConfig": "86"}, {"size": 2083, "mtime": 1753379942632, "results": "144", "hashOfConfig": "86"}, {"size": 355, "mtime": 1753379942632, "results": "145", "hashOfConfig": "86"}, {"size": 476, "mtime": 1753379942623, "results": "146", "hashOfConfig": "86"}, {"size": 45, "mtime": 1753379942616, "results": "147", "hashOfConfig": "86"}, {"size": 231, "mtime": 1753379942632, "results": "148", "hashOfConfig": "86"}, {"size": 97, "mtime": 1753379942632, "results": "149", "hashOfConfig": "86"}, {"size": 262, "mtime": 1753379942633, "results": "150", "hashOfConfig": "86"}, {"size": 610, "mtime": 1753379942614, "results": "151", "hashOfConfig": "86"}, {"size": 6749, "mtime": 1753379942622, "results": "152", "hashOfConfig": "86"}, {"size": 1070, "mtime": 1753379942620, "results": "153", "hashOfConfig": "86"}, {"size": 299, "mtime": 1753379942633, "results": "154", "hashOfConfig": "86"}, {"size": 3932, "mtime": 1753379942626, "results": "155", "hashOfConfig": "86"}, {"size": 5404, "mtime": 1753383746764, "results": "156", "hashOfConfig": "86"}, {"size": 2449, "mtime": 1753379942626, "results": "157", "hashOfConfig": "86"}, {"size": 3040, "mtime": 1753379942627, "results": "158", "hashOfConfig": "86"}, {"size": 5512, "mtime": 1753379942627, "results": "159", "hashOfConfig": "86"}, {"size": 68, "mtime": 1753379942616, "results": "160", "hashOfConfig": "86"}, {"size": 3831, "mtime": 1753379942610, "results": "161", "hashOfConfig": "86"}, {"size": 413, "mtime": 1753379942618, "results": "162", "hashOfConfig": "86"}, {"size": 135, "mtime": 1753379942616, "results": "163", "hashOfConfig": "86"}, {"size": 8989, "mtime": 1753379942622, "results": "164", "hashOfConfig": "86"}, {"size": 649, "mtime": 1753382456558, "results": "165", "hashOfConfig": "86"}, {"size": 3298, "mtime": 1753382883382, "results": "166", "hashOfConfig": "86"}, {"size": 372, "mtime": 1753381693125, "results": "167", "hashOfConfig": "86"}, {"size": 435, "mtime": 1753382915823, "results": "168", "hashOfConfig": "86"}, {"size": 1023, "mtime": 1753472666740, "results": "169", "hashOfConfig": "86"}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, "1jzqadv", {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "273"}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "307", "usedDeprecatedRules": "173"}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\index.tsx", [], [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\app\\App.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\app\\store.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\app\\routes.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\app\\events.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\app\\Layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\customer-service.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\order-service.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\zone-service.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\plant-service.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\auth\\require-auth.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\detail-slice.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\plants\\plants-slice.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\zones\\zones-slice.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\auth\\auth-provider.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\auth\\Login.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\zones\\detail-slice.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\orders-slice.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\plants\\detail-slice.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\customers\\customers-slice.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\customers\\detail-slice.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\users\\users-slice.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\driver-tasks\\driver-task-slice.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\zones\\Detail.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\users\\Detail.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\users\\List.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\driver-tasks\\Detail.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\driver-tasks\\List.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\driver-tasks\\New.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\zones\\List.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\customers\\Detail.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\plants\\List.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\customers\\List.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\plants\\Detail.tsx", ["425"], [], "import React, { useEffect, useState, useRef } from 'react';\r\nimport { useSelector, useDispatch } from 'react-redux';\r\nimport { useParams, useNavigate } from 'react-router';\r\nimport { Link } from 'react-router-dom';\r\nimport {\r\n  Button,\r\n  FormGroup,\r\n  Input,\r\n  InputGroup,\r\n  InputGroupText,\r\n  Label,\r\n  UncontrolledTooltip,\r\n  Dropdown,\r\n  DropdownToggle,\r\n  DropdownMenu,\r\n  DropdownItem,\r\n} from 'reactstrap';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { routes } from 'app/routes';\r\nimport { useAuth } from 'features/auth/use-auth';\r\nimport { selectPlants, generateAndSaveVarietySortOrder, moveVarietyAndSave, sortVariety } from './plants-slice';\r\nimport { deletePlant, savePlant, selectPlant, setPlant } from './detail-slice';\r\nimport { createPlant, Variety } from 'api/models/plants';\r\nimport { handleFocus } from 'utils/focus';\r\nimport { selectColours } from 'features/colours/colours-slice';\r\nimport { DndProvider, useDrag, useDrop } from 'react-dnd';\r\nimport { HTML5Backend } from 'react-dnd-html5-backend';\r\n\r\ninterface VarietyItemProps {\r\n  variety: Variety;\r\n  plant: any;\r\n  canUpdate: boolean;\r\n  colours: any[];\r\n  varietyDropdownOpen: {[key: string]: boolean};\r\n  onVarietyNameChange: (e: React.ChangeEvent<HTMLInputElement>, variety: Variety) => void;\r\n  onVarietyColourSelect: (variety: Variety, colourName: string) => void;\r\n  onDeleteVarietyClick: (variety: Variety) => void;\r\n  onToggleVarietyDropdown: (varietyName: string) => void;\r\n  onMoveVariety: (existingVariety: Variety, movingVariety: Variety) => void;\r\n}\r\n\r\nfunction VarietyItem({\r\n  variety,\r\n  plant,\r\n  canUpdate,\r\n  colours,\r\n  varietyDropdownOpen,\r\n  onVarietyNameChange,\r\n  onVarietyColourSelect,\r\n  onDeleteVarietyClick,\r\n  onToggleVarietyDropdown,\r\n  onMoveVariety\r\n}: VarietyItemProps) {\r\n  const ref = useRef<HTMLDivElement>(null);\r\n\r\n  const [, drag] = useDrag(() => ({\r\n    type: 'variety',\r\n    item: variety,\r\n    collect: (monitor) => ({\r\n      isDragging: monitor.isDragging(),\r\n    }),\r\n  }));\r\n\r\n  const [, drop] = useDrop(() => ({\r\n    accept: 'variety',\r\n    drop: (droppedVariety: Variety) => {\r\n      onMoveVariety(variety, droppedVariety);\r\n    },\r\n    collect: (monitor) => ({\r\n      isOver: monitor.isOver(),\r\n    }),\r\n  }));\r\n\r\n  drag(drop(ref));\r\n\r\n  return (\r\n    <div className=\"row\" ref={ref}>\r\n      {canUpdate && (\r\n        <div className=\"col-1\">\r\n          <div\r\n            className=\"cursor-move d-flex align-items-center justify-content-center\"\r\n            style={{ height: '38px' }}>\r\n            <FontAwesomeIcon icon={['fat', 'grip-vertical']} />\r\n          </div>\r\n        </div>\r\n      )}\r\n      <div className={canUpdate ? \"col-4\" : \"col-5\"}>\r\n        <Input\r\n          value={variety.name}\r\n          onChange={(e) => onVarietyNameChange(e, variety)}\r\n          disabled={!canUpdate}\r\n        />\r\n      </div>\r\n      <div className=\"col-3\">\r\n        <Dropdown\r\n          isOpen={varietyDropdownOpen[variety.name] || false}\r\n          toggle={() => onToggleVarietyDropdown(variety.name)}\r\n          disabled={!canUpdate}>\r\n          <DropdownToggle\r\n            caret\r\n            className=\"w-100 text-start d-flex align-items-center justify-content-between\"\r\n            style={{\r\n              backgroundColor: 'white',\r\n              borderColor: '#ced4da',\r\n              color: '#495057'\r\n            }}>\r\n            <div className=\"d-flex align-items-center\">\r\n              {variety.colour ? (\r\n                <>\r\n                  <div\r\n                    className='w16 h16 border rounded'\r\n                  />\r\n                  {variety.colour.name}\r\n                </>\r\n              ) : (\r\n                '---'\r\n              )}\r\n            </div>\r\n          </DropdownToggle>\r\n          <DropdownMenu className=\"w-100\">\r\n            <DropdownItem onClick={() => onVarietyColourSelect(variety, '')}>\r\n              ---\r\n            </DropdownItem>\r\n            {colours.map((c) => (\r\n              <DropdownItem\r\n                key={c.name}\r\n                onClick={() => onVarietyColourSelect(variety, c.name)}\r\n                className=\"d-flex align-items-center\">\r\n                <div\r\n                  style={{\r\n                    width: '16px',\r\n                    height: '16px',\r\n                    backgroundColor: c.hex,\r\n                    border: '1px solid #ccc',\r\n                    marginRight: '8px',\r\n                    borderRadius: '2px'\r\n                  }}\r\n                />\r\n                {c.name}\r\n              </DropdownItem>\r\n            ))}\r\n          </DropdownMenu>\r\n        </Dropdown>\r\n      </div>\r\n      {canUpdate && (\r\n        <div className=\"col-4\">\r\n          <Button\r\n            outline\r\n            color=\"danger\"\r\n            onClick={() => onDeleteVarietyClick(variety)}\r\n            size=\"sm\">\r\n            <FontAwesomeIcon icon={['fat', 'trash-alt']} />\r\n          </Button>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport function Detail() {\r\n  const dispatch = useDispatch(),\r\n    navigate = useNavigate(),\r\n    { isInRole } = useAuth(),\r\n    { id } = useParams<{ id: string }>(),\r\n    plants = useSelector(selectPlants),\r\n    plant = useSelector(selectPlant),\r\n    colours = useSelector(selectColours),\r\n    [hasVarieties, setHasVarieties] = useState(false),\r\n    [newVariety, setNewVariety] = useState(''),\r\n    [varietyDropdownOpen, setVarietyDropdownOpen] = useState<{[key: string]: boolean}>({}),\r\n    isNew = !plant._rev,\r\n    canUpdate =\r\n      (isNew && isInRole('create:plants')) || isInRole('update:plants'),\r\n    canDelete = isInRole('delete:plants');\r\n\r\n  useEffect(() => {\r\n    const found = plants.find((p) => p._id === id);\r\n    if (found && found._id !== plant._id) {\r\n      dispatch(setPlant(found));\r\n      setHasVarieties(!!found.varieties);\r\n      setNewVariety('');\r\n    } else if (id === 'new' && plant._rev) {\r\n      dispatch(setPlant(createPlant()));\r\n      setHasVarieties(false);\r\n      setNewVariety('');\r\n    }\r\n  }, [dispatch, id, plant, plants]);\r\n\r\n  useEffect(() => {\r\n    return function cleanup() {\r\n      dispatch(setPlant(createPlant()));\r\n    };\r\n  }, [dispatch]);\r\n\r\n  const handleSizeChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const size = e.target.value,\r\n      name = `${size} ${plant.crop}`,\r\n      update = { ...plant, size, name };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleCropChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const crop = e.target.value,\r\n      name = `${plant.size} ${crop}`,\r\n      update = { ...plant, crop, name };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleAbbreviationChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const abbreviation = e.target.value,\r\n      update = { ...plant, abbreviation };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleCuttingsPerPotChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    const cuttingsPerPot = e.target.valueAsNumber,\r\n      update = { ...plant, cuttingsPerPot };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleDefaultStickingCrewSizeChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    const defaultStickingCrewSize = e.target.valueAsNumber,\r\n      update = { ...plant, defaultStickingCrewSize };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleCuttingsPerTableTightChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    const cuttingsPerTableTight = e.target.valueAsNumber,\r\n      update = { ...plant, cuttingsPerTableTight };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleCuttingsPerTableSpacedChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    const cuttingsPerTableSpaced = e.target.valueAsNumber,\r\n      update = { ...plant, cuttingsPerTableSpaced };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleCuttingsPerTablePartiallySpacedChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    const cuttingsPerTablePartiallySpaced = e.target.valueAsNumber,\r\n      update = { ...plant, cuttingsPerTablePartiallySpaced };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handlePotsPerCaseChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const potsPerCase = e.target.valueAsNumber,\r\n      update = { ...plant, potsPerCase };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleHasLightsOutChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const hasLightsOut = e.target.checked,\r\n      update = { ...plant, hasLightsOut };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleHasPinchingChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const hasPinching = e.target.checked,\r\n      update = { ...plant, hasPinching };\r\n\r\n    if (!hasPinching) {\r\n      update.pinchingPotsPerHour = 0;\r\n      delete update.daysToPinch;\r\n    } else {\r\n      if (!update.daysToPinch) {\r\n        update.daysToPinch = 1;\r\n      }\r\n    }\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleDaysToPinchChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const daysToPinch = e.target.valueAsNumber || 0,\r\n      update = { ...plant, daysToPinch };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handlePinchingingPotsPerHourChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    const pinchingPotsPerHour = e.target.valueAsNumber,\r\n      update = { ...plant, pinchingPotsPerHour };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleColourChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const colour = e.target.value,\r\n      update = { ...plant };\r\n\r\n    if (colour) {\r\n      update.colour = colour;\r\n    } else {\r\n      delete plant.colour;\r\n    }\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleClearColourClick = () => {\r\n    const update = { ...plant };\r\n    delete update.colour;\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleAddColourClick = () => {\r\n    const update = { ...plant, colour: '#ffffff' };\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleHasVarietiesChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const hasVarieties = e.target.checked,\r\n      update = { ...plant };\r\n\r\n    if (hasVarieties) {\r\n      update.varieties = [];\r\n    } else {\r\n      delete update.varieties;\r\n    }\r\n\r\n    dispatch(setPlant(update));\r\n\r\n    setHasVarieties(hasVarieties);\r\n  };\r\n\r\n  const handleStickingCuttingsPerHourChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    const stickingCuttingsPerHour = e.target.valueAsNumber,\r\n      update = { ...plant, stickingCuttingsPerHour };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleSpacingPotsPerHourChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    const spacingPotsPerHour = e.target.valueAsNumber,\r\n      update = { ...plant, spacingPotsPerHour };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handlePackingCasesPerHourChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    const packingCasesPerHour = e.target.valueAsNumber,\r\n      update = { ...plant, packingCasesPerHour };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleNewVarietyChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const newVariety = e.target.value;\r\n    setNewVariety(newVariety);\r\n  };\r\n\r\n  const handleNewVarietyKeyUp = (e: React.KeyboardEvent) => {\r\n    if (newVariety && e.key === 'Enter') {\r\n      handleAddNewVarietyClick();\r\n    }\r\n  };\r\n\r\n  const handleAddNewVarietyClick = () => {\r\n    if (newVariety) {\r\n      const update = { ...plant },\r\n        varieties = (update.varieties || []).map((v) => ({ ...v }));\r\n\r\n      varieties.push({ name: newVariety, stickingSortOrder: null });\r\n\r\n      update.varieties = varieties;\r\n      dispatch(setPlant(update));\r\n      setNewVariety('');\r\n    }\r\n  };\r\n\r\n  const handleVarietyNameChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>,\r\n    variety: Variety\r\n  ) => {\r\n    const update = { ...plant },\r\n      varieties = (update.varieties || []).map((v) => ({ ...v })),\r\n      index = varieties.findIndex((v) => v.name === variety.name);\r\n\r\n    if (index !== -1) {\r\n      const name = e.target.value,\r\n        updated = { ...varieties[index], name };\r\n      varieties.splice(index, 1, updated);\r\n    }\r\n\r\n    update.varieties = varieties;\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n\r\n\r\n  const handleDeleteVarietyClick = (variety: Variety) => {\r\n    const update = { ...plant },\r\n      varieties = (update.varieties || []).map((v) => ({ ...v })),\r\n      index = varieties.findIndex((v) => v.name === variety.name);\r\n\r\n    if (index !== -1) {\r\n      varieties.splice(index, 1);\r\n    }\r\n\r\n    update.varieties = varieties;\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const toggleVarietyDropdown = (varietyName: string) => {\r\n    setVarietyDropdownOpen(prev => ({\r\n      ...prev,\r\n      [varietyName]: !prev[varietyName]\r\n    }));\r\n  };\r\n\r\n  const handleVarietyColourSelect = (variety: Variety, colourName: string) => {\r\n    const colour = colourName ? colours.find((c) => c.name === colourName) || null : null;\r\n    const update = { ...plant },\r\n      varieties = (update.varieties || []).map((v) => ({ ...v })),\r\n      index = varieties.findIndex((v) => v.name === variety.name);\r\n\r\n    if (index !== -1) {\r\n      const updated = { ...varieties[index], colour };\r\n      varieties.splice(index, 1, updated);\r\n    }\r\n\r\n    update.varieties = varieties;\r\n    dispatch(setPlant(update));\r\n\r\n    // Close the dropdown\r\n    setVarietyDropdownOpen(prev => ({\r\n      ...prev,\r\n      [variety.name]: false\r\n    }));\r\n  };\r\n\r\n  const handleMoveVariety = (existingVariety: Variety, movingVariety: Variety) => {\r\n    dispatch(moveVarietyAndSave({ plantId: plant._id, existingVariety, movingVariety }));\r\n  };\r\n\r\n  const handleSaveClick = async () => {\r\n    const result: any = await dispatch(savePlant());\r\n\r\n    if (!result.error) {\r\n      navigate(routes.plants.path);\r\n    }\r\n  };\r\n\r\n  const handleDeleteClick = async () => {\r\n    const result: any = await dispatch(deletePlant());\r\n\r\n    if (!result.error) {\r\n      navigate(routes.plants.path);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"container d-grid gap-3\">\r\n      <div className=\"row sticky-top-navbar my-2 py-2 bg-white shadow\">\r\n        <div className=\"col-auto pt-3\">\r\n          <Link to={routes.plants.path}>\r\n            <FontAwesomeIcon icon={['fat', 'chevron-left']} />\r\n            &nbsp; Back to Plants List\r\n          </Link>\r\n        </div>\r\n        <h1 className=\"col\">{isNew ? 'New Plant' : plant.name}</h1>\r\n      </div>\r\n      <div className=\"row\">\r\n        <div className=\"col-12 col-md-3\">\r\n          <label htmlFor=\"plant-size\">Size</label>\r\n          <Input\r\n            id=\"plant-size\"\r\n            value={plant.size}\r\n            onChange={handleSizeChange}\r\n            disabled={!canUpdate}\r\n          />\r\n        </div>\r\n        <div className=\"col-12 col-md-3\">\r\n          <label htmlFor=\"plant-crop\">Crop</label>\r\n          <Input\r\n            id=\"plant-crop\"\r\n            value={plant.crop}\r\n            onChange={handleCropChange}\r\n            disabled={!canUpdate}\r\n          />\r\n        </div>\r\n        <div className=\"col-12 col-md-3\">\r\n          <label htmlFor=\"plant-abbreviation\">Abbreviation</label>\r\n          <Input\r\n            id=\"plant-abbreviation\"\r\n            value={plant.abbreviation}\r\n            onChange={handleAbbreviationChange}\r\n            disabled={!canUpdate}\r\n          />\r\n        </div>\r\n      </div>\r\n      <div className=\"row\">\r\n        <div className=\"col-12 col-md-3\">\r\n          <label htmlFor=\"plant-cuttings-per-pot\">Cuttings per Pot</label>\r\n          <Input\r\n            id=\"plant-cuttings-per-pot\"\r\n            type=\"number\"\r\n            value={plant.cuttingsPerPot}\r\n            onChange={handleCuttingsPerPotChange}\r\n            onFocus={handleFocus}\r\n            disabled={!canUpdate}\r\n          />\r\n        </div>\r\n        <div className=\"col-12 col-md-3\">\r\n          <label htmlFor=\"plant-pots-per-case\">Pots Per Case</label>\r\n          <Input\r\n            id=\"plant-pots-per-case\"\r\n            type=\"number\"\r\n            value={plant.potsPerCase}\r\n            onChange={handlePotsPerCaseChange}\r\n            onFocus={handleFocus}\r\n            disabled={!canUpdate}\r\n          />\r\n        </div>\r\n        <div className=\"col-12 col-md-3\">\r\n          <label htmlFor=\"plant-default-sticking-crew-size\">\r\n            Default Sticking Crew Size\r\n          </label>\r\n          <Input\r\n            id=\"plant-default-sticking-crew-size\"\r\n            type=\"number\"\r\n            value={plant.defaultStickingCrewSize}\r\n            onChange={handleDefaultStickingCrewSizeChange}\r\n            onFocus={handleFocus}\r\n            disabled={!canUpdate}\r\n          />\r\n        </div>\r\n      </div>\r\n      <div className=\"row\">\r\n        <div className=\"col-12 col-md-3\">\r\n          <label htmlFor=\"plant-cuttings-per-table-tight\">\r\n            Cuttings Per Table: Tight\r\n          </label>\r\n          <Input\r\n            id=\"plant-cuttings-per-table-tight\"\r\n            type=\"number\"\r\n            value={plant.cuttingsPerTableTight}\r\n            onChange={handleCuttingsPerTableTightChange}\r\n            onFocus={handleFocus}\r\n            disabled={!canUpdate}\r\n          />\r\n        </div>\r\n        <div className=\"col-12 col-md-3\">\r\n          <label htmlFor=\"plant-cuttings-per-table-partially-spaced\">\r\n            Cuttings Per Table: Partially Spaced\r\n          </label>\r\n          <Input\r\n            id=\"plant-cuttings-per-table-partially-spaced\"\r\n            type=\"number\"\r\n            value={plant.cuttingsPerTablePartiallySpaced}\r\n            onChange={handleCuttingsPerTablePartiallySpacedChange}\r\n            onFocus={handleFocus}\r\n            disabled={!canUpdate}\r\n          />\r\n        </div>\r\n        <div className=\"col-12 col-md-3\">\r\n          <label htmlFor=\"plant-cuttings-per-table-spaced\">\r\n            Cuttings Per Table: Spaced\r\n          </label>\r\n          <Input\r\n            id=\"plant-cuttings-per-table-spaced\"\r\n            type=\"number\"\r\n            value={plant.cuttingsPerTableSpaced}\r\n            onChange={handleCuttingsPerTableSpacedChange}\r\n            onFocus={handleFocus}\r\n            disabled={!canUpdate}\r\n          />\r\n        </div>\r\n      </div>\r\n      <div className=\"row\">\r\n        <div className=\"col-12 col-md-3\">\r\n          <label htmlFor=\"plant-sticking-cuttings-per-hour\">\r\n            Sticking: Cuttings per Hour\r\n          </label>\r\n          <Input\r\n            id=\"plant-sticking-cuttings-per-hour\"\r\n            type=\"number\"\r\n            value={plant.stickingCuttingsPerHour}\r\n            onChange={handleStickingCuttingsPerHourChange}\r\n            onFocus={handleFocus}\r\n            disabled={!canUpdate}\r\n          />\r\n        </div>\r\n        <div className=\"col-12 col-md-3\">\r\n          <label htmlFor=\"plant-spacing-pots-per-hour\">\r\n            Spacing: Pots per Hour\r\n          </label>\r\n          <Input\r\n            id=\"plant-spacing-pots-per-hour\"\r\n            type=\"number\"\r\n            value={plant.spacingPotsPerHour}\r\n            onChange={handleSpacingPotsPerHourChange}\r\n            onFocus={handleFocus}\r\n            disabled={!canUpdate}\r\n          />\r\n        </div>\r\n        <div className=\"col-12 col-md-3\">\r\n          <label htmlFor=\"plant-packing-cases-per-hour\">\r\n            Packing: Cases per Hour\r\n          </label>\r\n          <Input\r\n            id=\"plant-packing-cases-per-hour\"\r\n            type=\"number\"\r\n            value={plant.packingCasesPerHour}\r\n            onChange={handlePackingCasesPerHourChange}\r\n            onFocus={handleFocus}\r\n            disabled={!canUpdate}\r\n          />\r\n        </div>\r\n        {!!plant.hasPinching && (\r\n          <div className=\"col-12 col-md-3\">\r\n            <label htmlFor=\"plant-pinching-pots-per-hour\">\r\n              Pinching: Pots per Hour\r\n            </label>\r\n            <Input\r\n              id=\"plant-pinching-pots-per-hour\"\r\n              type=\"number\"\r\n              value={plant.pinchingPotsPerHour}\r\n              onChange={handlePinchingingPotsPerHourChange}\r\n              onFocus={handleFocus}\r\n              disabled={!canUpdate}\r\n            />\r\n          </div>\r\n        )}\r\n      </div>\r\n      <div className=\"row my-3\">\r\n        <div className=\"col-12 col-md-3\">\r\n          <div className=\"form-check\">\r\n            <Input\r\n              id=\"plant-has-lights-out\"\r\n              type=\"checkbox\"\r\n              checked={plant.hasLightsOut}\r\n              onChange={handleHasLightsOutChange}\r\n              disabled={!canUpdate}\r\n            />\r\n            <label htmlFor=\"plant-has-lights-out\">Has Lights Out</label>\r\n          </div>\r\n        </div>\r\n        <div className=\"col-12 col-md-3\">\r\n          <div className=\"form-check\">\r\n            <Input\r\n              id=\"plant-needs-pinching\"\r\n              type=\"checkbox\"\r\n              checked={plant.hasPinching}\r\n              onChange={handleHasPinchingChange}\r\n              disabled={!canUpdate}\r\n            />\r\n            <label htmlFor=\"plant-needs-pinching\">Needs Pinching</label>\r\n          </div>\r\n          {!!plant.hasPinching && (\r\n            <FormGroup>\r\n              <label htmlFor=\"plant-pinching-pots-per-hour\">\r\n                Days after sticking to pinch\r\n              </label>\r\n              <Input\r\n                id=\"plant-pinching-pots-per-hour\"\r\n                type=\"number\"\r\n                value={plant.daysToPinch || ''}\r\n                onChange={handleDaysToPinchChange}\r\n                onFocus={handleFocus}\r\n                disabled={!canUpdate}\r\n              />\r\n            </FormGroup>\r\n          )}\r\n        </div>\r\n        <div className=\"col-12 col-md-3\">\r\n          <label htmlFor=\"plant-colour\">Colour</label>\r\n          {!!plant.colour && (\r\n            <InputGroup>\r\n              <Input\r\n                id=\"plant-colour\"\r\n                type=\"color\"\r\n                value={plant.colour || ''}\r\n                onChange={handleColourChange}\r\n                disabled={!canUpdate}\r\n              />\r\n              {canUpdate && (\r\n                <Button\r\n                  size=\"sm\"\r\n                  color=\"danger\"\r\n                  outline\r\n                  onClick={handleClearColourClick}>\r\n                  <FontAwesomeIcon icon={['fat', 'trash']} />\r\n                </Button>\r\n              )}\r\n            </InputGroup>\r\n          )}\r\n          {!plant.colour && (\r\n            <InputGroup>\r\n              <InputGroupText>No Colour</InputGroupText>\r\n              {canUpdate && (\r\n                <>\r\n                  <Button\r\n                    id=\"add-colour\"\r\n                    size=\"sm\"\r\n                    color=\"success\"\r\n                    outline\r\n                    onClick={handleAddColourClick}>\r\n                    <FontAwesomeIcon icon={['fat', 'palette']} />\r\n                  </Button>\r\n                  <UncontrolledTooltip target=\"add-colour\">\r\n                    Add Colour\r\n                  </UncontrolledTooltip>\r\n                </>\r\n              )}\r\n            </InputGroup>\r\n          )}\r\n        </div>\r\n      </div>\r\n      <div className=\"row\">\r\n        <div className=\"col-12 col-md-3\">\r\n          <div className=\"form-check\">\r\n            <Input\r\n              id=\"plant-has-varieties\"\r\n              type=\"checkbox\"\r\n              checked={hasVarieties}\r\n              onChange={handleHasVarietiesChange}\r\n              disabled={!canUpdate}\r\n            />\r\n            <label htmlFor=\"plant-has-varieties\">Has Varieties</label>\r\n          </div>\r\n        </div>\r\n        {hasVarieties && (\r\n          <div className=\"col-6\">\r\n            {canUpdate && (\r\n              <div className=\"row\">\r\n                <div className=\"col-5\">\r\n                  <FormGroup floating>\r\n                    <Input\r\n                      id=\"new-variety-name\"\r\n                      value={newVariety}\r\n                      onChange={handleNewVarietyChange}\r\n                      onKeyUp={handleNewVarietyKeyUp}\r\n                      placeholder=\"Add Variety\"\r\n                    />\r\n                    <Label htmlFor=\"new-variety-name\">Add Variety</Label>\r\n                  </FormGroup>\r\n                </div>\r\n                <div className=\"col-4\">\r\n                  <Button\r\n                    outline\r\n                    color=\"success\"\r\n                    onClick={handleAddNewVarietyClick}\r\n                    disabled={!newVariety}\r\n                    size=\"sm\">\r\n                    <FontAwesomeIcon icon={['fat', 'plus']} />\r\n                  </Button>\r\n                </div>\r\n              </div>\r\n            )}\r\n            <DndProvider backend={HTML5Backend}>\r\n              {plant.varieties?.sort(sortVariety).map((variety) => (\r\n                <VarietyItem\r\n                  key={variety.name}\r\n                  variety={variety}\r\n                  plant={plant}\r\n                  canUpdate={canUpdate}\r\n                  colours={colours}\r\n                  varietyDropdownOpen={varietyDropdownOpen}\r\n                  onVarietyNameChange={handleVarietyNameChange}\r\n                  onVarietyColourSelect={handleVarietyColourSelect}\r\n                  onDeleteVarietyClick={handleDeleteVarietyClick}\r\n                  onToggleVarietyDropdown={toggleVarietyDropdown}\r\n                  onMoveVariety={handleMoveVariety}\r\n                />\r\n              ))}\r\n            </DndProvider>\r\n          </div>\r\n        )}\r\n      </div>\r\n      <div className=\"row sticky-bottom bg-white border-top py-2\">\r\n        {!isNew && canDelete && (\r\n          <div className=\"col-auto\">\r\n            <Button\r\n              onClick={handleDeleteClick}\r\n              outline\r\n              color=\"danger\"\r\n              size=\"lg\"\r\n              className=\"me-auto\">\r\n              <FontAwesomeIcon icon={['fat', 'trash-alt']} />\r\n              &nbsp; Delete\r\n            </Button>\r\n          </div>\r\n        )}\r\n        <div className=\"col text-end\">\r\n          <Button tag={Link} to={routes.plants.path} outline size=\"lg\">\r\n            {canUpdate ? 'Cancel' : 'Close'}\r\n          </Button>\r\n          {canUpdate && (\r\n            <>\r\n              &nbsp;\r\n              <Button onClick={handleSaveClick} color=\"success\" size=\"lg\">\r\n                <FontAwesomeIcon icon={['fat', 'save']} />\r\n                &nbsp; Save\r\n              </Button>\r\n            </>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Detail;\r\n", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\ByStickDate.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\ByPinchDate.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\ByFlowerDate.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\Detail.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\List.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\service-base.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\app\\NavMenu.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\BySpaceDate.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\colours\\Detail.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\pages\\Index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\colours\\List.tsx", ["426"], [], "import { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { routes } from \"app/routes\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { Link } from \"react-router-dom\";\r\nimport { Button } from \"reactstrap\";\r\nimport { selectColours } from \"./colours-slice\";\r\nimport { useAuth } from \"features/auth/use-auth\";\r\n\r\nexport function List() { \r\n  const colours = useSelector(selectColours),\r\n    { isInRole } = useAuth(),\r\n    canCreate = isInRole('create:colours');\r\n\r\n  return (\r\n    <div className=\"container d-grid gap-2\">\r\n      <div className=\"row sticky-top-navbar bg-white mt-2 py-2 border-bottom shadow\">\r\n        <h1 className=\"col pt-2\">\r\n          <FontAwesomeIcon icon={['fat', 'palette']} />\r\n          &nbsp;\r\n          Colours List\r\n        </h1>\r\n        {/*canCreate &&\r\n          <div className=\"col-auto pt-3\">\r\n            <Button tag={Link} to={routes.colours.routes.new()} outline color=\"success\">\r\n              <FontAwesomeIcon icon={['fat', 'plus']} />\r\n              &nbsp;\r\n              New Colour\r\n            </Button>\r\n          </div>\r\n        */}\r\n        <div className=\"col-auto pt-3\">\r\n          <Button tag={Link} to={routes.colours.routes.new()} outline color=\"success\">\r\n            <FontAwesomeIcon icon={['fat', 'plus']} />\r\n            &nbsp;\r\n            New Colour\r\n          </Button>\r\n        </div>\r\n      </div>\r\n      <table className=\"table\">\r\n        <thead>\r\n          <tr className=\"sticky-top bg-white\" style={{top: '140px'}}>\r\n            <th>&nbsp;</th>\r\n            <th>Name</th>\r\n            <th>Hex</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          {colours.map(colour =>\r\n            <tr key={colour._id}>\r\n              <td>\r\n                <Link to={routes.colours.routes.detail.to(colour._id)}>\r\n                  <FontAwesomeIcon icon={['fat', 'edit']} />\r\n                </Link>\r\n              </td>\r\n              <td>{colour.name}</td>\r\n              <td>{colour.hex}</td>\r\n            </tr>\r\n            )}\r\n        </tbody>\r\n      </table>      \r\n    </div>\r\n  )\r\n}\r\n\r\nexport default List;", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\auth\\auth-context.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\auth\\use-auth.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\reports-service.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\auth-service.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\driver-tasks-service.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\models\\orders.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\models\\plants.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\models\\zones.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\models\\driver-tasks.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\models\\customers.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\sort.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\loading\\Loading.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\problem-details.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\format.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\equals.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\error\\Error.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\boot\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\focus.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\class-names.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\weeks.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\notifications-service.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\driver-tasks\\ListFilters.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\driver-tasks\\List-Date.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\guid.ts", [], ["427", "428"], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\OrderRow.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\app\\database.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\LabourReport.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\SalesWeekRow.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\Variety.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\boot\\axios.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\api-base.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\auth\\configuration.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\boot\\font-awesome.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\driver-tasks\\List-Item.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\colours\\colours-slice.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\colours\\detail-slice.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\models\\colour.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\colour-service.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\plants\\List-Item.tsx", [], [], {"ruleId": "429", "severity": 1, "message": "430", "line": 21, "column": 24, "nodeType": "431", "messageId": "432", "endLine": 21, "endColumn": 55}, {"ruleId": "429", "severity": 1, "message": "433", "line": 12, "column": 5, "nodeType": "431", "messageId": "432", "endLine": 12, "endColumn": 14}, {"ruleId": "434", "severity": 1, "message": "435", "line": 5, "column": 60, "nodeType": "436", "messageId": "437", "endLine": 5, "endColumn": 61, "suppressions": "438"}, {"ruleId": "434", "severity": 1, "message": "435", "line": 5, "column": 66, "nodeType": "436", "messageId": "437", "endLine": 5, "endColumn": 67, "suppressions": "439"}, "@typescript-eslint/no-unused-vars", "'generateAndSaveVarietySortOrder' is defined but never used.", "Identifier", "unusedVar", "'canCreate' is assigned a value but never used.", "no-mixed-operators", "Unexpected mix of '&' and '|'. Use parentheses to clarify the intended order of operations.", "BinaryExpression", "unexpectedMixedOperator", ["440"], ["441"], {"kind": "442", "justification": "443"}, {"kind": "442", "justification": "443"}, "directive", ""]