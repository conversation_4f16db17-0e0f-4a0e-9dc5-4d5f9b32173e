[{"C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\app\\App.tsx": "2", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\app\\store.ts": "3", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\app\\routes.ts": "4", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\app\\events.ts": "5", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\app\\Layout.tsx": "6", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\customer-service.ts": "7", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\order-service.ts": "8", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\zone-service.ts": "9", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\plant-service.ts": "10", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\auth\\require-auth.tsx": "11", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\detail-slice.ts": "12", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\plants\\plants-slice.ts": "13", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\zones\\zones-slice.ts": "14", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\auth\\auth-provider.tsx": "15", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\auth\\Login.tsx": "16", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\zones\\detail-slice.ts": "17", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\orders-slice.ts": "18", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\plants\\detail-slice.ts": "19", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\customers\\customers-slice.ts": "20", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\customers\\detail-slice.ts": "21", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\users\\users-slice.ts": "22", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\driver-tasks\\driver-task-slice.ts": "23", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\zones\\Detail.tsx": "24", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\users\\Detail.tsx": "25", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\users\\List.tsx": "26", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\driver-tasks\\Detail.tsx": "27", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\driver-tasks\\List.tsx": "28", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\driver-tasks\\New.tsx": "29", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\zones\\List.tsx": "30", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\customers\\Detail.tsx": "31", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\plants\\List.tsx": "32", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\customers\\List.tsx": "33", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\plants\\Detail.tsx": "34", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\ByStickDate.tsx": "35", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\ByPinchDate.tsx": "36", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\ByFlowerDate.tsx": "37", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\Detail.tsx": "38", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\List.tsx": "39", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\service-base.ts": "40", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\app\\NavMenu.tsx": "41", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\BySpaceDate.tsx": "42", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\colours\\Detail.tsx": "43", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\pages\\Index.tsx": "44", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\colours\\List.tsx": "45", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\auth\\auth-context.ts": "46", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\auth\\use-auth.ts": "47", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\reports-service.ts": "48", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\auth-service.ts": "49", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\driver-tasks-service.ts": "50", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\models\\orders.ts": "51", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\models\\plants.ts": "52", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\models\\zones.ts": "53", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\models\\driver-tasks.ts": "54", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\models\\customers.ts": "55", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\sort.ts": "56", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\loading\\Loading.tsx": "57", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\problem-details.ts": "58", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\format.ts": "59", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\equals.ts": "60", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\error\\Error.tsx": "61", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\boot\\index.ts": "62", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\focus.ts": "63", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\class-names.ts": "64", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\weeks.ts": "65", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\notifications-service.ts": "66", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\driver-tasks\\ListFilters.tsx": "67", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\driver-tasks\\List-Date.tsx": "68", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\guid.ts": "69", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\OrderRow.tsx": "70", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\app\\database.ts": "71", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\LabourReport.tsx": "72", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\SalesWeekRow.tsx": "73", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\Variety.tsx": "74", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\boot\\axios.ts": "75", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\api-base.ts": "76", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\auth\\configuration.ts": "77", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\boot\\font-awesome.ts": "78", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\driver-tasks\\List-Item.tsx": "79", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\colours\\colours-slice.ts": "80", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\colours\\detail-slice.ts": "81", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\models\\colour.ts": "82", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\colour-service.ts": "83", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\plants\\List-Item.tsx": "84"}, {"size": 675, "mtime": 1753379942630, "results": "85", "hashOfConfig": "86"}, {"size": 6534, "mtime": 1753383787294, "results": "87", "hashOfConfig": "86"}, {"size": 1194, "mtime": 1753382476769, "results": "88", "hashOfConfig": "86"}, {"size": 3150, "mtime": 1753381281451, "results": "89", "hashOfConfig": "86"}, {"size": 832, "mtime": 1753383706024, "results": "90", "hashOfConfig": "86"}, {"size": 189, "mtime": 1753379942615, "results": "91", "hashOfConfig": "86"}, {"size": 380, "mtime": 1753379942611, "results": "92", "hashOfConfig": "86"}, {"size": 882, "mtime": 1753379942614, "results": "93", "hashOfConfig": "86"}, {"size": 340, "mtime": 1753379942615, "results": "94", "hashOfConfig": "86"}, {"size": 470, "mtime": 1753463240525, "results": "95", "hashOfConfig": "86"}, {"size": 604, "mtime": 1753379942619, "results": "96", "hashOfConfig": "86"}, {"size": 3382, "mtime": 1753379942627, "results": "97", "hashOfConfig": "86"}, {"size": 5756, "mtime": 1753472768461, "results": "98", "hashOfConfig": "86"}, {"size": 694, "mtime": 1753379942630, "results": "99", "hashOfConfig": "86"}, {"size": 1669, "mtime": 1753379942618, "results": "100", "hashOfConfig": "86"}, {"size": 2313, "mtime": 1753379942618, "results": "101", "hashOfConfig": "86"}, {"size": 3177, "mtime": 1753379942630, "results": "102", "hashOfConfig": "86"}, {"size": 7906, "mtime": 1753379942627, "results": "103", "hashOfConfig": "86"}, {"size": 3843, "mtime": 1753473478650, "results": "104", "hashOfConfig": "86"}, {"size": 766, "mtime": 1753379942620, "results": "105", "hashOfConfig": "86"}, {"size": 3384, "mtime": 1753379942620, "results": "106", "hashOfConfig": "86"}, {"size": 1854, "mtime": 1753379942628, "results": "107", "hashOfConfig": "86"}, {"size": 6721, "mtime": 1753379942622, "results": "108", "hashOfConfig": "86"}, {"size": 4788, "mtime": 1753379942630, "results": "109", "hashOfConfig": "86"}, {"size": 16200, "mtime": 1753379942628, "results": "110", "hashOfConfig": "86"}, {"size": 2126, "mtime": 1753379942628, "results": "111", "hashOfConfig": "86"}, {"size": 9857, "mtime": 1753379942620, "results": "112", "hashOfConfig": "86"}, {"size": 4221, "mtime": 1753379942622, "results": "113", "hashOfConfig": "86"}, {"size": 9032, "mtime": 1753379942622, "results": "114", "hashOfConfig": "86"}, {"size": 2229, "mtime": 1753379942630, "results": "115", "hashOfConfig": "86"}, {"size": 4014, "mtime": 1753379942619, "results": "116", "hashOfConfig": "86"}, {"size": 1822, "mtime": 1753472699848, "results": "117", "hashOfConfig": "86"}, {"size": 1870, "mtime": 1753379942619, "results": "118", "hashOfConfig": "86"}, {"size": 26425, "mtime": 1753473101601, "results": "119", "hashOfConfig": "86"}, {"size": 9851, "mtime": 1753379942625, "results": "120", "hashOfConfig": "86"}, {"size": 10007, "mtime": 1753379942623, "results": "121", "hashOfConfig": "86"}, {"size": 10445, "mtime": 1753379942623, "results": "122", "hashOfConfig": "86"}, {"size": 47495, "mtime": 1753379942626, "results": "123", "hashOfConfig": "86"}, {"size": 10641, "mtime": 1753379942626, "results": "124", "hashOfConfig": "86"}, {"size": 3133, "mtime": 1753463229684, "results": "125", "hashOfConfig": "86"}, {"size": 5358, "mtime": 1753382309129, "results": "126", "hashOfConfig": "86"}, {"size": 9798, "mtime": 1753379942625, "results": "127", "hashOfConfig": "86"}, {"size": 4659, "mtime": 1753384126919, "results": "128", "hashOfConfig": "86"}, {"size": 506, "mtime": 1753379942630, "results": "129", "hashOfConfig": "86"}, {"size": 2093, "mtime": 1753383827780, "results": "130", "hashOfConfig": "86"}, {"size": 362, "mtime": 1753379942618, "results": "131", "hashOfConfig": "86"}, {"size": 149, "mtime": 1753379942619, "results": "132", "hashOfConfig": "86"}, {"size": 4200, "mtime": 1753379942614, "results": "133", "hashOfConfig": "86"}, {"size": 2526, "mtime": 1753379942611, "results": "134", "hashOfConfig": "86"}, {"size": 2754, "mtime": 1753379942611, "results": "135", "hashOfConfig": "86"}, {"size": 2779, "mtime": 1753379942613, "results": "136", "hashOfConfig": "86"}, {"size": 1497, "mtime": 1753473148834, "results": "137", "hashOfConfig": "86"}, {"size": 494, "mtime": 1753379942613, "results": "138", "hashOfConfig": "86"}, {"size": 1320, "mtime": 1753379942612, "results": "139", "hashOfConfig": "86"}, {"size": 635, "mtime": 1753379942612, "results": "140", "hashOfConfig": "86"}, {"size": 1276, "mtime": 1753379942633, "results": "141", "hashOfConfig": "86"}, {"size": 221, "mtime": 1753379942623, "results": "142", "hashOfConfig": "86"}, {"size": 591, "mtime": 1753379942633, "results": "143", "hashOfConfig": "86"}, {"size": 2083, "mtime": 1753379942632, "results": "144", "hashOfConfig": "86"}, {"size": 355, "mtime": 1753379942632, "results": "145", "hashOfConfig": "86"}, {"size": 476, "mtime": 1753379942623, "results": "146", "hashOfConfig": "86"}, {"size": 45, "mtime": 1753379942616, "results": "147", "hashOfConfig": "86"}, {"size": 231, "mtime": 1753379942632, "results": "148", "hashOfConfig": "86"}, {"size": 97, "mtime": 1753379942632, "results": "149", "hashOfConfig": "86"}, {"size": 262, "mtime": 1753379942633, "results": "150", "hashOfConfig": "86"}, {"size": 610, "mtime": 1753379942614, "results": "151", "hashOfConfig": "86"}, {"size": 6749, "mtime": 1753379942622, "results": "152", "hashOfConfig": "86"}, {"size": 1070, "mtime": 1753379942620, "results": "153", "hashOfConfig": "86"}, {"size": 299, "mtime": 1753379942633, "results": "154", "hashOfConfig": "86"}, {"size": 3932, "mtime": 1753379942626, "results": "155", "hashOfConfig": "86"}, {"size": 5404, "mtime": 1753383746764, "results": "156", "hashOfConfig": "86"}, {"size": 2449, "mtime": 1753379942626, "results": "157", "hashOfConfig": "86"}, {"size": 3040, "mtime": 1753379942627, "results": "158", "hashOfConfig": "86"}, {"size": 5512, "mtime": 1753379942627, "results": "159", "hashOfConfig": "86"}, {"size": 68, "mtime": 1753379942616, "results": "160", "hashOfConfig": "86"}, {"size": 3831, "mtime": 1753379942610, "results": "161", "hashOfConfig": "86"}, {"size": 413, "mtime": 1753379942618, "results": "162", "hashOfConfig": "86"}, {"size": 135, "mtime": 1753379942616, "results": "163", "hashOfConfig": "86"}, {"size": 8989, "mtime": 1753379942622, "results": "164", "hashOfConfig": "86"}, {"size": 649, "mtime": 1753382456558, "results": "165", "hashOfConfig": "86"}, {"size": 3298, "mtime": 1753382883382, "results": "166", "hashOfConfig": "86"}, {"size": 372, "mtime": 1753381693125, "results": "167", "hashOfConfig": "86"}, {"size": 435, "mtime": 1753382915823, "results": "168", "hashOfConfig": "86"}, {"size": 1023, "mtime": 1753472666740, "results": "169", "hashOfConfig": "86"}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, "1jzqadv", {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "306", "usedDeprecatedRules": "173"}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "173"}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\index.tsx", [], [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\app\\App.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\app\\store.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\app\\routes.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\app\\events.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\app\\Layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\customer-service.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\order-service.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\zone-service.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\plant-service.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\auth\\require-auth.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\detail-slice.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\plants\\plants-slice.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\zones\\zones-slice.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\auth\\auth-provider.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\auth\\Login.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\zones\\detail-slice.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\orders-slice.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\plants\\detail-slice.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\customers\\customers-slice.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\customers\\detail-slice.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\users\\users-slice.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\driver-tasks\\driver-task-slice.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\zones\\Detail.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\users\\Detail.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\users\\List.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\driver-tasks\\Detail.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\driver-tasks\\List.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\driver-tasks\\New.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\zones\\List.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\customers\\Detail.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\plants\\List.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\customers\\List.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\plants\\Detail.tsx", ["424"], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\ByStickDate.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\ByPinchDate.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\ByFlowerDate.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\Detail.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\List.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\service-base.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\app\\NavMenu.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\BySpaceDate.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\colours\\Detail.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\pages\\Index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\colours\\List.tsx", ["425"], [], "import { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { routes } from \"app/routes\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { Link } from \"react-router-dom\";\r\nimport { Button } from \"reactstrap\";\r\nimport { selectColours } from \"./colours-slice\";\r\nimport { useAuth } from \"features/auth/use-auth\";\r\n\r\nexport function List() { \r\n  const colours = useSelector(selectColours),\r\n    { isInRole } = useAuth(),\r\n    canCreate = isInRole('create:colours');\r\n\r\n  return (\r\n    <div className=\"container d-grid gap-2\">\r\n      <div className=\"row sticky-top-navbar bg-white mt-2 py-2 border-bottom shadow\">\r\n        <h1 className=\"col pt-2\">\r\n          <FontAwesomeIcon icon={['fat', 'palette']} />\r\n          &nbsp;\r\n          Colours List\r\n        </h1>\r\n        {/*canCreate &&\r\n          <div className=\"col-auto pt-3\">\r\n            <Button tag={Link} to={routes.colours.routes.new()} outline color=\"success\">\r\n              <FontAwesomeIcon icon={['fat', 'plus']} />\r\n              &nbsp;\r\n              New Colour\r\n            </Button>\r\n          </div>\r\n        */}\r\n        <div className=\"col-auto pt-3\">\r\n          <Button tag={Link} to={routes.colours.routes.new()} outline color=\"success\">\r\n            <FontAwesomeIcon icon={['fat', 'plus']} />\r\n            &nbsp;\r\n            New Colour\r\n          </Button>\r\n        </div>\r\n      </div>\r\n      <table className=\"table\">\r\n        <thead>\r\n          <tr className=\"sticky-top bg-white\" style={{top: '140px'}}>\r\n            <th>&nbsp;</th>\r\n            <th>Name</th>\r\n            <th>Hex</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          {colours.map(colour =>\r\n            <tr key={colour._id}>\r\n              <td>\r\n                <Link to={routes.colours.routes.detail.to(colour._id)}>\r\n                  <FontAwesomeIcon icon={['fat', 'edit']} />\r\n                </Link>\r\n              </td>\r\n              <td>{colour.name}</td>\r\n              <td>{colour.hex}</td>\r\n            </tr>\r\n            )}\r\n        </tbody>\r\n      </table>      \r\n    </div>\r\n  )\r\n}\r\n\r\nexport default List;", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\auth\\auth-context.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\auth\\use-auth.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\reports-service.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\auth-service.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\driver-tasks-service.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\models\\orders.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\models\\plants.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\models\\zones.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\models\\driver-tasks.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\models\\customers.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\sort.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\loading\\Loading.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\problem-details.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\format.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\equals.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\error\\Error.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\boot\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\focus.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\class-names.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\weeks.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\notifications-service.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\driver-tasks\\ListFilters.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\driver-tasks\\List-Date.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\guid.ts", [], ["426", "427"], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\OrderRow.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\app\\database.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\LabourReport.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\SalesWeekRow.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\Variety.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\boot\\axios.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\api-base.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\auth\\configuration.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\boot\\font-awesome.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\driver-tasks\\List-Item.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\colours\\colours-slice.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\colours\\detail-slice.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\models\\colour.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\colour-service.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\plants\\List-Item.tsx", [], [], {"ruleId": "428", "severity": 1, "message": "429", "line": 21, "column": 24, "nodeType": "430", "messageId": "431", "endLine": 21, "endColumn": 55}, {"ruleId": "428", "severity": 1, "message": "432", "line": 12, "column": 5, "nodeType": "430", "messageId": "431", "endLine": 12, "endColumn": 14}, {"ruleId": "433", "severity": 1, "message": "434", "line": 5, "column": 60, "nodeType": "435", "messageId": "436", "endLine": 5, "endColumn": 61, "suppressions": "437"}, {"ruleId": "433", "severity": 1, "message": "434", "line": 5, "column": 66, "nodeType": "435", "messageId": "436", "endLine": 5, "endColumn": 67, "suppressions": "438"}, "@typescript-eslint/no-unused-vars", "'generateAndSaveVarietySortOrder' is defined but never used.", "Identifier", "unusedVar", "'canCreate' is assigned a value but never used.", "no-mixed-operators", "Unexpected mix of '&' and '|'. Use parentheses to clarify the intended order of operations.", "BinaryExpression", "unexpectedMixedOperator", ["439"], ["440"], {"kind": "441", "justification": "442"}, {"kind": "441", "justification": "442"}, "directive", ""]