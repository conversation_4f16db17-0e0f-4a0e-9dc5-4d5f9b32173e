{"ast": null, "code": "import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';\nimport { plantApi } from 'api/plant-service';\nimport { sortBy, sortSizeName } from 'utils/sort';\nconst initialState = {\n  plants: []\n};\nexport const generateAndSaveVarietySortOrder = createAsyncThunk('plants/generate-and-save-variety-sort-order', async (plantId, _ref) => {\n  let {\n    getState,\n    rejectWithValue\n  } = _ref;\n  try {\n    const state = getState();\n    const plant = state.plants.plants.find(p => p._id === plantId);\n    if (!plant || !plant.varieties) {\n      throw new Error('Plant or varieties not found');\n    }\n\n    // Check if all varieties have null stickingSortOrder\n    const allHaveNullSortOrder = plant.varieties.every(v => v.stickingSortOrder == null);\n    if (allHaveNullSortOrder) {\n      // Sort varieties by name and assign stickingSortOrder\n      const sortedVarieties = [...plant.varieties].sort((a, b) => a.name.localeCompare(b.name));\n      const varietiesWithSortOrder = sortedVarieties.map((variety, index) => ({\n        ...variety,\n        stickingSortOrder: index\n      }));\n\n      // Update the plant with the new variety sort order\n      const updatedPlant = {\n        ...plant,\n        varieties: varietiesWithSortOrder\n      };\n      const savedPlant = await plantApi.save(updatedPlant);\n      return savedPlant;\n    }\n\n    // If not all have null sort order, just return the current plant\n    return plant;\n  } catch (e) {\n    return rejectWithValue(e);\n  }\n});\nexport const moveVarietyAndSave = createAsyncThunk('plants/move-variety-and-save', async (_ref2, _ref3) => {\n  let {\n    plantId,\n    existingVariety,\n    movingVariety\n  } = _ref2;\n  let {\n    getState,\n    rejectWithValue\n  } = _ref3;\n  try {\n    const state = getState();\n    const plant = state.plants.plants.find(p => p._id === plantId);\n    if (!plant || !plant.varieties) {\n      throw new Error('Plant or varieties not found');\n    }\n    let varieties = [...plant.varieties];\n\n    // If existingVariety has null stickingSortOrder, generate sort orders for all varieties first\n    if (existingVariety.stickingSortOrder == null) {\n      varieties = varieties.sort((a, b) => a.name.localeCompare(b.name)).map((v, index) => ({\n        ...v,\n        stickingSortOrder: index\n      }));\n    }\n    const existingVarietyIndex = varieties.findIndex(v => v.name === existingVariety.name);\n\n    // Move the dropped variety to the existing variety position, and move the existing variety and all subsequent varieties up by 1\n    const updatedVarieties = varieties.map(v => {\n      if (v.name === existingVariety.name) {\n        return {\n          ...v,\n          stickingSortOrder: existingVarietyIndex + 1\n        };\n      } else if (v.name === movingVariety.name) {\n        return {\n          ...v,\n          stickingSortOrder: existingVarietyIndex\n        };\n      } else if (v.stickingSortOrder != null && v.stickingSortOrder >= existingVarietyIndex) {\n        return {\n          ...v,\n          stickingSortOrder: v.stickingSortOrder + 1\n        };\n      }\n      return v;\n    });\n\n    // Update and save the plant with the new variety order\n    const updatedPlant = {\n      ...plant,\n      varieties: updatedVarieties\n    };\n    const savedPlant = await plantApi.save(updatedPlant);\n    return savedPlant;\n  } catch (e) {\n    return rejectWithValue(e);\n  }\n});\nexport const plantsSlice = createSlice({\n  name: 'plants',\n  initialState,\n  reducers: {\n    setPlants(state, action) {\n      state.plants = action.payload;\n    }\n  },\n  extraReducers: builder => {\n    builder.addCase(generateAndSaveVarietySortOrder.fulfilled, (state, action) => {\n      // Update the plant in state with the saved version that has variety sort orders\n      const savedPlant = action.payload;\n      const index = state.plants.findIndex(p => p._id === savedPlant._id);\n      if (index !== -1) {\n        state.plants[index] = savedPlant;\n      }\n    }).addCase(moveVarietyAndSave.fulfilled, (state, action) => {\n      // Update the plant in state with the saved version after variety move\n      const savedPlant = action.payload;\n      const index = state.plants.findIndex(p => p._id === savedPlant._id);\n      if (index !== -1) {\n        state.plants[index] = savedPlant;\n      }\n    });\n  }\n});\nexport const {\n  setPlants\n} = plantsSlice.actions;\nexport const selectPlants = state => state.plants.plants.map(p => ({\n  ...p\n})).sort(sortPlant);\nexport default plantsSlice.reducer;\nconst sortByCrop = sortBy('crop');\nfunction sortPlant(a, b) {\n  return sortByCrop(a, b) || sortSizeName(a.size, b.size);\n}\nfunction sortByVarietyStickingSortOrder(a, b) {\n  // Handle null values - place them last\n  if (a.stickingSortOrder == null && b.stickingSortOrder == null) return 0;\n  if (a.stickingSortOrder != null && b.stickingSortOrder == null) return -1; // a comes before b (non-null before null)\n  if (a.stickingSortOrder == null && b.stickingSortOrder != null) return 1; // b comes before a (non-null before null)\n\n  // Both are non-null, compare normally\n  return a.stickingSortOrder - b.stickingSortOrder;\n}\nfunction sortVariety(a, b) {\n  return sortByVarietyStickingSortOrder(a, b) || a.name.localeCompare(b.name);\n}", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "plantApi", "sortBy", "sortSizeName", "initialState", "plants", "generateAndSaveVarietySortOrder", "plantId", "getState", "rejectWithValue", "state", "plant", "find", "p", "_id", "varieties", "Error", "allHaveNullSortOrder", "every", "v", "stickingSortOrder", "sortedVarieties", "sort", "a", "b", "name", "localeCompare", "varietiesWithSortOrder", "map", "variety", "index", "updatedPlant", "savedPlant", "save", "e", "moveVarietyAndSave", "existingVariety", "movingVariety", "existingVarietyIndex", "findIndex", "updatedVarieties", "plantsSlice", "reducers", "setPlants", "action", "payload", "extraReducers", "builder", "addCase", "fulfilled", "actions", "selectPlants", "sortPlant", "reducer", "sortByCrop", "size", "sortByVarietyStickingSortOrder", "sortVariety"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/plants/plants-slice.ts"], "sourcesContent": ["import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';\r\nimport { Plant, Variety } from 'api/models/plants';\r\nimport { plantApi } from 'api/plant-service';\r\nimport { RootState } from 'app/store';\r\nimport { sortBy, sortSizeName } from 'utils/sort';\r\nimport { ProblemDetails } from 'utils/problem-details';\r\n\r\nexport interface PlantsState {\r\n  plants: Plant[];\r\n}\r\n\r\nconst initialState: PlantsState = {\r\n  plants: []\r\n};\r\n\r\nexport const generateAndSaveVarietySortOrder = createAsyncThunk<Plant, string, { state: RootState }>(\r\n  'plants/generate-and-save-variety-sort-order',\r\n  async (plantId, { getState, rejectWithValue }) => {\r\n    try {\r\n      const state = getState();\r\n      const plant = state.plants.plants.find(p => p._id === plantId);\r\n\r\n      if (!plant || !plant.varieties) {\r\n        throw new Error('Plant or varieties not found');\r\n      }\r\n\r\n      // Check if all varieties have null stickingSortOrder\r\n      const allHaveNullSortOrder = plant.varieties.every(v => v.stickingSortOrder == null);\r\n\r\n      if (allHaveNullSortOrder) {\r\n        // Sort varieties by name and assign stickingSortOrder\r\n        const sortedVarieties = [...plant.varieties].sort((a, b) => a.name.localeCompare(b.name));\r\n        const varietiesWithSortOrder = sortedVarieties.map((variety, index) => ({\r\n          ...variety,\r\n          stickingSortOrder: index\r\n        }));\r\n\r\n        // Update the plant with the new variety sort order\r\n        const updatedPlant = { ...plant, varieties: varietiesWithSortOrder };\r\n        const savedPlant = await plantApi.save(updatedPlant);\r\n        return savedPlant;\r\n      }\r\n\r\n      // If not all have null sort order, just return the current plant\r\n      return plant;\r\n    } catch (e) {\r\n      return rejectWithValue(e as ProblemDetails);\r\n    }\r\n  }\r\n);\r\n\r\nexport const moveVarietyAndSave = createAsyncThunk<Plant, { plantId: string, existingVariety: Variety, movingVariety: Variety }, { state: RootState }>(\r\n  'plants/move-variety-and-save',\r\n  async ({ plantId, existingVariety, movingVariety }, { getState, rejectWithValue }) => {\r\n    try {\r\n      const state = getState();\r\n      const plant = state.plants.plants.find(p => p._id === plantId);\r\n\r\n      if (!plant || !plant.varieties) {\r\n        throw new Error('Plant or varieties not found');\r\n      }\r\n\r\n      let varieties = [...plant.varieties];\r\n\r\n      // If existingVariety has null stickingSortOrder, generate sort orders for all varieties first\r\n      if (existingVariety.stickingSortOrder == null) {\r\n        varieties = varieties.sort((a, b) => a.name.localeCompare(b.name)).map((v, index) => ({ ...v, stickingSortOrder: index }));\r\n      }\r\n\r\n      const existingVarietyIndex = varieties.findIndex(v => v.name === existingVariety.name);\r\n\r\n      // Move the dropped variety to the existing variety position, and move the existing variety and all subsequent varieties up by 1\r\n      const updatedVarieties = varieties.map(v => {\r\n        if (v.name === existingVariety.name) {\r\n          return { ...v, stickingSortOrder: existingVarietyIndex + 1 };\r\n        } else if (v.name === movingVariety.name) {\r\n          return { ...v, stickingSortOrder: existingVarietyIndex };\r\n        } else if (v.stickingSortOrder != null && v.stickingSortOrder >= existingVarietyIndex) {\r\n          return { ...v, stickingSortOrder: v.stickingSortOrder + 1 };\r\n        }\r\n        return v;\r\n      });\r\n\r\n      // Update and save the plant with the new variety order\r\n      const updatedPlant = { ...plant, varieties: updatedVarieties };\r\n      const savedPlant = await plantApi.save(updatedPlant);\r\n      return savedPlant;\r\n    } catch (e) {\r\n      return rejectWithValue(e as ProblemDetails);\r\n    }\r\n  }\r\n);\r\n\r\n\r\n\r\nexport const plantsSlice = createSlice({\r\n  name: 'plants',\r\n  initialState,\r\n  reducers: {\r\n    setPlants(state, action: PayloadAction<Plant[]>) {\r\n      state.plants = action.payload;\r\n    }\r\n  },\r\n  extraReducers: (builder) => {\r\n    builder\r\n      .addCase(generateAndSaveVarietySortOrder.fulfilled, (state, action) => {\r\n        // Update the plant in state with the saved version that has variety sort orders\r\n        const savedPlant = action.payload;\r\n        const index = state.plants.findIndex(p => p._id === savedPlant._id);\r\n        if (index !== -1) {\r\n          state.plants[index] = savedPlant;\r\n        }\r\n      })\r\n      .addCase(moveVarietyAndSave.fulfilled, (state, action) => {\r\n        // Update the plant in state with the saved version after variety move\r\n        const savedPlant = action.payload;\r\n        const index = state.plants.findIndex(p => p._id === savedPlant._id);\r\n        if (index !== -1) {\r\n          state.plants[index] = savedPlant;\r\n        }\r\n      });\r\n  }\r\n});\r\n\r\nexport const { setPlants } = plantsSlice.actions;\r\n\r\nexport const selectPlants = (state: RootState) => state.plants.plants.map(p => ({...p})).sort(sortPlant);\r\n\r\nexport default plantsSlice.reducer;\r\n\r\nconst sortByCrop = sortBy('crop');\r\n\r\nfunction sortPlant(a: Plant, b: Plant) {\r\n  return sortByCrop(a, b) || sortSizeName(a.size, b.size);\r\n}\r\n\r\nfunction sortByVarietyStickingSortOrder(a: Variety, b: Variety) {\r\n  // Handle null values - place them last\r\n  if (a.stickingSortOrder == null && b.stickingSortOrder == null) return 0;\r\n  if (a.stickingSortOrder != null && b.stickingSortOrder == null) return -1; // a comes before b (non-null before null)\r\n  if (a.stickingSortOrder == null && b.stickingSortOrder != null) return 1;  // b comes before a (non-null before null)\r\n\r\n  // Both are non-null, compare normally\r\n  return a.stickingSortOrder! - b.stickingSortOrder!;\r\n}\r\n\r\nfunction sortVariety(a: Variety, b: Variety) {\r\n  return sortByVarietyStickingSortOrder(a, b) || a.name.localeCompare(b.name);\r\n}"], "mappings": "AAAA,SAASA,WAAW,EAAiBC,gBAAgB,QAAQ,kBAAkB;AAE/E,SAASC,QAAQ,QAAQ,mBAAmB;AAE5C,SAASC,MAAM,EAAEC,YAAY,QAAQ,YAAY;AAOjD,MAAMC,YAAyB,GAAG;EAChCC,MAAM,EAAE;AACV,CAAC;AAED,OAAO,MAAMC,+BAA+B,GAAGN,gBAAgB,CAC7D,6CAA6C,EAC7C,OAAOO,OAAO,WAAoC;EAAA,IAAlC;IAAEC,QAAQ;IAAEC;EAAgB,CAAC;EAC3C,IAAI;IACF,MAAMC,KAAK,GAAGF,QAAQ,EAAE;IACxB,MAAMG,KAAK,GAAGD,KAAK,CAACL,MAAM,CAACA,MAAM,CAACO,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKP,OAAO,CAAC;IAE9D,IAAI,CAACI,KAAK,IAAI,CAACA,KAAK,CAACI,SAAS,EAAE;MAC9B,MAAM,IAAIC,KAAK,CAAC,8BAA8B,CAAC;IACjD;;IAEA;IACA,MAAMC,oBAAoB,GAAGN,KAAK,CAACI,SAAS,CAACG,KAAK,CAACC,CAAC,IAAIA,CAAC,CAACC,iBAAiB,IAAI,IAAI,CAAC;IAEpF,IAAIH,oBAAoB,EAAE;MACxB;MACA,MAAMI,eAAe,GAAG,CAAC,GAAGV,KAAK,CAACI,SAAS,CAAC,CAACO,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,IAAI,CAACC,aAAa,CAACF,CAAC,CAACC,IAAI,CAAC,CAAC;MACzF,MAAME,sBAAsB,GAAGN,eAAe,CAACO,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,MAAM;QACtE,GAAGD,OAAO;QACVT,iBAAiB,EAAEU;MACrB,CAAC,CAAC,CAAC;;MAEH;MACA,MAAMC,YAAY,GAAG;QAAE,GAAGpB,KAAK;QAAEI,SAAS,EAAEY;MAAuB,CAAC;MACpE,MAAMK,UAAU,GAAG,MAAM/B,QAAQ,CAACgC,IAAI,CAACF,YAAY,CAAC;MACpD,OAAOC,UAAU;IACnB;;IAEA;IACA,OAAOrB,KAAK;EACd,CAAC,CAAC,OAAOuB,CAAC,EAAE;IACV,OAAOzB,eAAe,CAACyB,CAAC,CAAmB;EAC7C;AACF,CAAC,CACF;AAED,OAAO,MAAMC,kBAAkB,GAAGnC,gBAAgB,CAChD,8BAA8B,EAC9B,wBAAsF;EAAA,IAA/E;IAAEO,OAAO;IAAE6B,eAAe;IAAEC;EAAc,CAAC;EAAA,IAAE;IAAE7B,QAAQ;IAAEC;EAAgB,CAAC;EAC/E,IAAI;IACF,MAAMC,KAAK,GAAGF,QAAQ,EAAE;IACxB,MAAMG,KAAK,GAAGD,KAAK,CAACL,MAAM,CAACA,MAAM,CAACO,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKP,OAAO,CAAC;IAE9D,IAAI,CAACI,KAAK,IAAI,CAACA,KAAK,CAACI,SAAS,EAAE;MAC9B,MAAM,IAAIC,KAAK,CAAC,8BAA8B,CAAC;IACjD;IAEA,IAAID,SAAS,GAAG,CAAC,GAAGJ,KAAK,CAACI,SAAS,CAAC;;IAEpC;IACA,IAAIqB,eAAe,CAAChB,iBAAiB,IAAI,IAAI,EAAE;MAC7CL,SAAS,GAAGA,SAAS,CAACO,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,IAAI,CAACC,aAAa,CAACF,CAAC,CAACC,IAAI,CAAC,CAAC,CAACG,GAAG,CAAC,CAACT,CAAC,EAAEW,KAAK,MAAM;QAAE,GAAGX,CAAC;QAAEC,iBAAiB,EAAEU;MAAM,CAAC,CAAC,CAAC;IAC5H;IAEA,MAAMQ,oBAAoB,GAAGvB,SAAS,CAACwB,SAAS,CAACpB,CAAC,IAAIA,CAAC,CAACM,IAAI,KAAKW,eAAe,CAACX,IAAI,CAAC;;IAEtF;IACA,MAAMe,gBAAgB,GAAGzB,SAAS,CAACa,GAAG,CAACT,CAAC,IAAI;MAC1C,IAAIA,CAAC,CAACM,IAAI,KAAKW,eAAe,CAACX,IAAI,EAAE;QACnC,OAAO;UAAE,GAAGN,CAAC;UAAEC,iBAAiB,EAAEkB,oBAAoB,GAAG;QAAE,CAAC;MAC9D,CAAC,MAAM,IAAInB,CAAC,CAACM,IAAI,KAAKY,aAAa,CAACZ,IAAI,EAAE;QACxC,OAAO;UAAE,GAAGN,CAAC;UAAEC,iBAAiB,EAAEkB;QAAqB,CAAC;MAC1D,CAAC,MAAM,IAAInB,CAAC,CAACC,iBAAiB,IAAI,IAAI,IAAID,CAAC,CAACC,iBAAiB,IAAIkB,oBAAoB,EAAE;QACrF,OAAO;UAAE,GAAGnB,CAAC;UAAEC,iBAAiB,EAAED,CAAC,CAACC,iBAAiB,GAAG;QAAE,CAAC;MAC7D;MACA,OAAOD,CAAC;IACV,CAAC,CAAC;;IAEF;IACA,MAAMY,YAAY,GAAG;MAAE,GAAGpB,KAAK;MAAEI,SAAS,EAAEyB;IAAiB,CAAC;IAC9D,MAAMR,UAAU,GAAG,MAAM/B,QAAQ,CAACgC,IAAI,CAACF,YAAY,CAAC;IACpD,OAAOC,UAAU;EACnB,CAAC,CAAC,OAAOE,CAAC,EAAE;IACV,OAAOzB,eAAe,CAACyB,CAAC,CAAmB;EAC7C;AACF,CAAC,CACF;AAID,OAAO,MAAMO,WAAW,GAAG1C,WAAW,CAAC;EACrC0B,IAAI,EAAE,QAAQ;EACdrB,YAAY;EACZsC,QAAQ,EAAE;IACRC,SAAS,CAACjC,KAAK,EAAEkC,MAA8B,EAAE;MAC/ClC,KAAK,CAACL,MAAM,GAAGuC,MAAM,CAACC,OAAO;IAC/B;EACF,CAAC;EACDC,aAAa,EAAGC,OAAO,IAAK;IAC1BA,OAAO,CACJC,OAAO,CAAC1C,+BAA+B,CAAC2C,SAAS,EAAE,CAACvC,KAAK,EAAEkC,MAAM,KAAK;MACrE;MACA,MAAMZ,UAAU,GAAGY,MAAM,CAACC,OAAO;MACjC,MAAMf,KAAK,GAAGpB,KAAK,CAACL,MAAM,CAACkC,SAAS,CAAC1B,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKkB,UAAU,CAAClB,GAAG,CAAC;MACnE,IAAIgB,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBpB,KAAK,CAACL,MAAM,CAACyB,KAAK,CAAC,GAAGE,UAAU;MAClC;IACF,CAAC,CAAC,CACDgB,OAAO,CAACb,kBAAkB,CAACc,SAAS,EAAE,CAACvC,KAAK,EAAEkC,MAAM,KAAK;MACxD;MACA,MAAMZ,UAAU,GAAGY,MAAM,CAACC,OAAO;MACjC,MAAMf,KAAK,GAAGpB,KAAK,CAACL,MAAM,CAACkC,SAAS,CAAC1B,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKkB,UAAU,CAAClB,GAAG,CAAC;MACnE,IAAIgB,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBpB,KAAK,CAACL,MAAM,CAACyB,KAAK,CAAC,GAAGE,UAAU;MAClC;IACF,CAAC,CAAC;EACN;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEW;AAAU,CAAC,GAAGF,WAAW,CAACS,OAAO;AAEhD,OAAO,MAAMC,YAAY,GAAIzC,KAAgB,IAAKA,KAAK,CAACL,MAAM,CAACA,MAAM,CAACuB,GAAG,CAACf,CAAC,KAAK;EAAC,GAAGA;AAAC,CAAC,CAAC,CAAC,CAACS,IAAI,CAAC8B,SAAS,CAAC;AAExG,eAAeX,WAAW,CAACY,OAAO;AAElC,MAAMC,UAAU,GAAGpD,MAAM,CAAC,MAAM,CAAC;AAEjC,SAASkD,SAAS,CAAC7B,CAAQ,EAAEC,CAAQ,EAAE;EACrC,OAAO8B,UAAU,CAAC/B,CAAC,EAAEC,CAAC,CAAC,IAAIrB,YAAY,CAACoB,CAAC,CAACgC,IAAI,EAAE/B,CAAC,CAAC+B,IAAI,CAAC;AACzD;AAEA,SAASC,8BAA8B,CAACjC,CAAU,EAAEC,CAAU,EAAE;EAC9D;EACA,IAAID,CAAC,CAACH,iBAAiB,IAAI,IAAI,IAAII,CAAC,CAACJ,iBAAiB,IAAI,IAAI,EAAE,OAAO,CAAC;EACxE,IAAIG,CAAC,CAACH,iBAAiB,IAAI,IAAI,IAAII,CAAC,CAACJ,iBAAiB,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;EAC3E,IAAIG,CAAC,CAACH,iBAAiB,IAAI,IAAI,IAAII,CAAC,CAACJ,iBAAiB,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC,CAAE;;EAE3E;EACA,OAAOG,CAAC,CAACH,iBAAiB,GAAII,CAAC,CAACJ,iBAAkB;AACpD;AAEA,SAASqC,WAAW,CAAClC,CAAU,EAAEC,CAAU,EAAE;EAC3C,OAAOgC,8BAA8B,CAACjC,CAAC,EAAEC,CAAC,CAAC,IAAID,CAAC,CAACE,IAAI,CAACC,aAAa,CAACF,CAAC,CAACC,IAAI,CAAC;AAC7E"}, "metadata": {}, "sourceType": "module"}