import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';
import { Plant, Variety } from 'api/models/plants';
import { plantApi } from 'api/plant-service';
import { RootState } from 'app/store';
import { sortBy, sortSizeName } from 'utils/sort';
import { ProblemDetails } from 'utils/problem-details';

export interface PlantsState {
  plants: Plant[];
}

const initialState: PlantsState = {
  plants: []
};

export const generateAndSaveVarietySortOrder = createAsyncThunk<Plant, string, { state: RootState }>(
  'plants/generate-and-save-variety-sort-order',
  async (plantId, { getState, rejectWithValue }) => {
    try {
      const state = getState();
      const plant = state.plants.plants.find(p => p._id === plantId);

      if (!plant || !plant.varieties) {
        throw new Error('Plant or varieties not found');
      }

      // Check if all varieties have null stickingSortOrder
      const allHaveNullSortOrder = plant.varieties.every(v => v.stickingSortOrder == null);

      if (allHaveNullSortOrder) {
        // Sort varieties by name and assign stickingSortOrder
        const sortedVarieties = [...plant.varieties].sort((a, b) => a.name.localeCompare(b.name));
        const varietiesWithSortOrder = sortedVarieties.map((variety, index) => ({
          ...variety,
          stickingSortOrder: index
        }));

        // Update the plant with the new variety sort order
        const updatedPlant = { ...plant, varieties: varietiesWithSortOrder };
        const savedPlant = await plantApi.save(updatedPlant);
        return savedPlant;
      }

      // If not all have null sort order, just return the current plant
      return plant;
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const moveVarietyAndSave = createAsyncThunk<Plant, { plantId: string, existingVariety: Variety, movingVariety: Variety }, { state: RootState }>(
  'plants/move-variety-and-save',
  async ({ plantId, existingVariety, movingVariety }, { getState, rejectWithValue }) => {
    try {
      const state = getState();
      const plant = state.plants.plants.find(p => p._id === plantId);

      if (!plant || !plant.varieties) {
        throw new Error('Plant or varieties not found');
      }

      let varieties = [...plant.varieties];

      // If existingVariety has null stickingSortOrder, generate sort orders for all varieties first
      if (existingVariety.stickingSortOrder == null) {
        varieties = varieties.sort((a, b) => a.name.localeCompare(b.name)).map((v, index) => ({ ...v, stickingSortOrder: index }));
      }

      const existingVarietyIndex = varieties.findIndex(v => v.name === existingVariety.name);

      // Move the dropped variety to the existing variety position, and move the existing variety and all subsequent varieties up by 1
      const updatedVarieties = varieties.map(v => {
        if (v.name === existingVariety.name) {
          return { ...v, stickingSortOrder: existingVarietyIndex + 1 };
        } else if (v.name === movingVariety.name) {
          return { ...v, stickingSortOrder: existingVarietyIndex };
        } else if (v.stickingSortOrder != null && v.stickingSortOrder >= existingVarietyIndex) {
          return { ...v, stickingSortOrder: v.stickingSortOrder + 1 };
        }
        return v;
      });

      // Update and save the plant with the new variety order
      const updatedPlant = { ...plant, varieties: updatedVarieties };
      const savedPlant = await plantApi.save(updatedPlant);
      return savedPlant;
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);



export const plantsSlice = createSlice({
  name: 'plants',
  initialState,
  reducers: {
    setPlants(state, action: PayloadAction<Plant[]>) {
      state.plants = action.payload;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(generateAndSaveVarietySortOrder.fulfilled, (state, action) => {
        // Update the plant in state with the saved version that has variety sort orders
        const savedPlant = action.payload;
        const index = state.plants.findIndex(p => p._id === savedPlant._id);
        if (index !== -1) {
          state.plants[index] = savedPlant;
        }
      })
      .addCase(moveVarietyAndSave.fulfilled, (state, action) => {
        // Update the plant in state with the saved version after variety move
        const savedPlant = action.payload;
        const index = state.plants.findIndex(p => p._id === savedPlant._id);
        if (index !== -1) {
          state.plants[index] = savedPlant;
        }
      });
  }
});

export const { setPlants } = plantsSlice.actions;

export const selectPlants = (state: RootState) => state.plants.plants.map(p => ({...p})).sort(sortPlant);

export default plantsSlice.reducer;

const sortByCrop = sortBy('crop');

function sortPlant(a: Plant, b: Plant) {
  return sortByCrop(a, b) || sortSizeName(a.size, b.size);
}

function sortByVarietyStickingSortOrder(a: Variety, b: Variety) {
  // Handle null values - place them last
  if (a.stickingSortOrder == null && b.stickingSortOrder == null) return 0;
  if (a.stickingSortOrder != null && b.stickingSortOrder == null) return -1; // a comes before b (non-null before null)
  if (a.stickingSortOrder == null && b.stickingSortOrder != null) return 1;  // b comes before a (non-null before null)

  // Both are non-null, compare normally
  return a.stickingSortOrder! - b.stickingSortOrder!;
}

export function sortVariety(a: Variety, b: Variety) {
  return sortByVarietyStickingSortOrder(a, b) || a.name.localeCompare(b.name);
}