{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\GreenRP\\\\Web\\\\ClientApp\\\\src\\\\features\\\\plants\\\\Detail.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { useParams, useNavigate } from 'react-router';\nimport { Link } from 'react-router-dom';\nimport { Button, FormGroup, Input, InputGroup, InputGroupText, Label, UncontrolledTooltip, Dropdown, DropdownToggle, DropdownMenu, DropdownItem } from 'reactstrap';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { routes } from 'app/routes';\nimport { useAuth } from 'features/auth/use-auth';\nimport { selectPlants, moveVarietyAndSave } from './plants-slice';\nimport { deletePlant, savePlant, selectPlant, setPlant } from './detail-slice';\nimport { createPlant } from 'api/models/plants';\nimport { handleFocus } from 'utils/focus';\nimport { selectColours } from 'features/colours/colours-slice';\nimport { useDrag, useDrop } from 'react-dnd';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction VarietyItem(_ref) {\n  _s();\n  let {\n    variety,\n    plant,\n    canUpdate,\n    colours,\n    varietyDropdownOpen,\n    onVarietyNameChange,\n    onVarietyColourSelect,\n    onDeleteVarietyClick,\n    onToggleVarietyDropdown,\n    onMoveVariety\n  } = _ref;\n  const ref = useRef(null);\n  const [, drag] = useDrag(() => ({\n    type: 'variety',\n    item: variety,\n    collect: monitor => ({\n      isDragging: monitor.isDragging()\n    })\n  }));\n  const [, drop] = useDrop(() => ({\n    accept: 'variety',\n    drop: droppedVariety => {\n      onMoveVariety(variety, droppedVariety);\n    },\n    collect: monitor => ({\n      isOver: monitor.isOver()\n    })\n  }));\n  drag(drop(ref));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"row\",\n    ref: ref,\n    children: [canUpdate && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"col-1\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"cursor-move d-flex align-items-center justify-content-center\",\n        style: {\n          height: '38px'\n        },\n        children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n          icon: ['fat', 'grip-vertical']\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: canUpdate ? \"col-4\" : \"col-5\",\n      children: /*#__PURE__*/_jsxDEV(Input, {\n        value: variety.name,\n        onChange: e => onVarietyNameChange(e, variety),\n        disabled: !canUpdate\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"col-3\",\n      children: /*#__PURE__*/_jsxDEV(Dropdown, {\n        isOpen: varietyDropdownOpen[variety.name] || false,\n        toggle: () => onToggleVarietyDropdown(variety.name),\n        disabled: !canUpdate,\n        children: [/*#__PURE__*/_jsxDEV(DropdownToggle, {\n          caret: true,\n          className: \"w-100 text-start d-flex align-items-center justify-content-between\",\n          style: {\n            backgroundColor: 'white',\n            borderColor: '#ced4da',\n            color: '#495057'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex align-items-center\",\n            children: variety.colour ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w16 h16 border rounded\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 19\n              }, this), variety.colour.name]\n            }, void 0, true) : '---'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DropdownMenu, {\n          className: \"w-100\",\n          children: [/*#__PURE__*/_jsxDEV(DropdownItem, {\n            onClick: () => onVarietyColourSelect(variety, ''),\n            children: \"---\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this), colours.map(c => /*#__PURE__*/_jsxDEV(DropdownItem, {\n            onClick: () => onVarietyColourSelect(variety, c.name),\n            className: \"d-flex align-items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '16px',\n                height: '16px',\n                backgroundColor: c.hex,\n                border: '1px solid #ccc',\n                marginRight: '8px',\n                borderRadius: '2px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 17\n            }, this), c.name]\n          }, c.name, true, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 7\n    }, this), canUpdate && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"col-4\",\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        outline: true,\n        color: \"danger\",\n        onClick: () => onDeleteVarietyClick(variety),\n        size: \"sm\",\n        children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n          icon: ['fat', 'trash-alt']\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 77,\n    columnNumber: 5\n  }, this);\n}\n_s(VarietyItem, \"X3CddqULbvfUG8W1f735mdMTt/4=\", false, function () {\n  return [useDrag, useDrop];\n});\n_c = VarietyItem;\nexport function Detail() {\n  _s2();\n  var _plant$varieties;\n  const dispatch = useDispatch(),\n    navigate = useNavigate(),\n    {\n      isInRole\n    } = useAuth(),\n    {\n      id\n    } = useParams(),\n    plants = useSelector(selectPlants),\n    plant = useSelector(selectPlant),\n    colours = useSelector(selectColours),\n    [hasVarieties, setHasVarieties] = useState(false),\n    [newVariety, setNewVariety] = useState(''),\n    [varietyDropdownOpen, setVarietyDropdownOpen] = useState({}),\n    isNew = !plant._rev,\n    canUpdate = isNew && isInRole('create:plants') || isInRole('update:plants'),\n    canDelete = isInRole('delete:plants');\n  useEffect(() => {\n    const found = plants.find(p => p._id === id);\n    if (found && found._id !== plant._id) {\n      dispatch(setPlant(found));\n      setHasVarieties(!!found.varieties);\n      setNewVariety('');\n    } else if (id === 'new' && plant._rev) {\n      dispatch(setPlant(createPlant()));\n      setHasVarieties(false);\n      setNewVariety('');\n    }\n  }, [dispatch, id, plant, plants]);\n  useEffect(() => {\n    return function cleanup() {\n      dispatch(setPlant(createPlant()));\n    };\n  }, [dispatch]);\n  const handleSizeChange = e => {\n    const size = e.target.value,\n      name = `${size} ${plant.crop}`,\n      update = {\n        ...plant,\n        size,\n        name\n      };\n    dispatch(setPlant(update));\n  };\n  const handleCropChange = e => {\n    const crop = e.target.value,\n      name = `${plant.size} ${crop}`,\n      update = {\n        ...plant,\n        crop,\n        name\n      };\n    dispatch(setPlant(update));\n  };\n  const handleAbbreviationChange = e => {\n    const abbreviation = e.target.value,\n      update = {\n        ...plant,\n        abbreviation\n      };\n    dispatch(setPlant(update));\n  };\n  const handleCuttingsPerPotChange = e => {\n    const cuttingsPerPot = e.target.valueAsNumber,\n      update = {\n        ...plant,\n        cuttingsPerPot\n      };\n    dispatch(setPlant(update));\n  };\n  const handleDefaultStickingCrewSizeChange = e => {\n    const defaultStickingCrewSize = e.target.valueAsNumber,\n      update = {\n        ...plant,\n        defaultStickingCrewSize\n      };\n    dispatch(setPlant(update));\n  };\n  const handleCuttingsPerTableTightChange = e => {\n    const cuttingsPerTableTight = e.target.valueAsNumber,\n      update = {\n        ...plant,\n        cuttingsPerTableTight\n      };\n    dispatch(setPlant(update));\n  };\n  const handleCuttingsPerTableSpacedChange = e => {\n    const cuttingsPerTableSpaced = e.target.valueAsNumber,\n      update = {\n        ...plant,\n        cuttingsPerTableSpaced\n      };\n    dispatch(setPlant(update));\n  };\n  const handleCuttingsPerTablePartiallySpacedChange = e => {\n    const cuttingsPerTablePartiallySpaced = e.target.valueAsNumber,\n      update = {\n        ...plant,\n        cuttingsPerTablePartiallySpaced\n      };\n    dispatch(setPlant(update));\n  };\n  const handlePotsPerCaseChange = e => {\n    const potsPerCase = e.target.valueAsNumber,\n      update = {\n        ...plant,\n        potsPerCase\n      };\n    dispatch(setPlant(update));\n  };\n  const handleHasLightsOutChange = e => {\n    const hasLightsOut = e.target.checked,\n      update = {\n        ...plant,\n        hasLightsOut\n      };\n    dispatch(setPlant(update));\n  };\n  const handleHasPinchingChange = e => {\n    const hasPinching = e.target.checked,\n      update = {\n        ...plant,\n        hasPinching\n      };\n    if (!hasPinching) {\n      update.pinchingPotsPerHour = 0;\n      delete update.daysToPinch;\n    } else {\n      if (!update.daysToPinch) {\n        update.daysToPinch = 1;\n      }\n    }\n    dispatch(setPlant(update));\n  };\n  const handleDaysToPinchChange = e => {\n    const daysToPinch = e.target.valueAsNumber || 0,\n      update = {\n        ...plant,\n        daysToPinch\n      };\n    dispatch(setPlant(update));\n  };\n  const handlePinchingingPotsPerHourChange = e => {\n    const pinchingPotsPerHour = e.target.valueAsNumber,\n      update = {\n        ...plant,\n        pinchingPotsPerHour\n      };\n    dispatch(setPlant(update));\n  };\n  const handleColourChange = e => {\n    const colour = e.target.value,\n      update = {\n        ...plant\n      };\n    if (colour) {\n      update.colour = colour;\n    } else {\n      delete plant.colour;\n    }\n    dispatch(setPlant(update));\n  };\n  const handleClearColourClick = () => {\n    const update = {\n      ...plant\n    };\n    delete update.colour;\n    dispatch(setPlant(update));\n  };\n  const handleAddColourClick = () => {\n    const update = {\n      ...plant,\n      colour: '#ffffff'\n    };\n    dispatch(setPlant(update));\n  };\n  const handleHasVarietiesChange = e => {\n    const hasVarieties = e.target.checked,\n      update = {\n        ...plant\n      };\n    if (hasVarieties) {\n      update.varieties = [];\n    } else {\n      delete update.varieties;\n    }\n    dispatch(setPlant(update));\n    setHasVarieties(hasVarieties);\n  };\n  const handleStickingCuttingsPerHourChange = e => {\n    const stickingCuttingsPerHour = e.target.valueAsNumber,\n      update = {\n        ...plant,\n        stickingCuttingsPerHour\n      };\n    dispatch(setPlant(update));\n  };\n  const handleSpacingPotsPerHourChange = e => {\n    const spacingPotsPerHour = e.target.valueAsNumber,\n      update = {\n        ...plant,\n        spacingPotsPerHour\n      };\n    dispatch(setPlant(update));\n  };\n  const handlePackingCasesPerHourChange = e => {\n    const packingCasesPerHour = e.target.valueAsNumber,\n      update = {\n        ...plant,\n        packingCasesPerHour\n      };\n    dispatch(setPlant(update));\n  };\n  const handleNewVarietyChange = e => {\n    const newVariety = e.target.value;\n    setNewVariety(newVariety);\n  };\n  const handleNewVarietyKeyUp = e => {\n    if (newVariety && e.key === 'Enter') {\n      handleAddNewVarietyClick();\n    }\n  };\n  const handleAddNewVarietyClick = () => {\n    if (newVariety) {\n      const update = {\n          ...plant\n        },\n        varieties = (update.varieties || []).map(v => ({\n          ...v\n        }));\n      varieties.push({\n        name: newVariety,\n        stickingSortOrder: null\n      });\n      update.varieties = varieties;\n      dispatch(setPlant(update));\n      setNewVariety('');\n    }\n  };\n  const handleVarietyNameChange = (e, variety) => {\n    const update = {\n        ...plant\n      },\n      varieties = (update.varieties || []).map(v => ({\n        ...v\n      })),\n      index = varieties.findIndex(v => v.name === variety.name);\n    if (index !== -1) {\n      const name = e.target.value,\n        updated = {\n          ...varieties[index],\n          name\n        };\n      varieties.splice(index, 1, updated);\n    }\n    update.varieties = varieties;\n    dispatch(setPlant(update));\n  };\n  const handleDeleteVarietyClick = variety => {\n    const update = {\n        ...plant\n      },\n      varieties = (update.varieties || []).map(v => ({\n        ...v\n      })),\n      index = varieties.findIndex(v => v.name === variety.name);\n    if (index !== -1) {\n      varieties.splice(index, 1);\n    }\n    update.varieties = varieties;\n    dispatch(setPlant(update));\n  };\n  const toggleVarietyDropdown = varietyName => {\n    setVarietyDropdownOpen(prev => ({\n      ...prev,\n      [varietyName]: !prev[varietyName]\n    }));\n  };\n  const handleVarietyColourSelect = (variety, colourName) => {\n    const colour = colourName ? colours.find(c => c.name === colourName) || null : null;\n    const update = {\n        ...plant\n      },\n      varieties = (update.varieties || []).map(v => ({\n        ...v\n      })),\n      index = varieties.findIndex(v => v.name === variety.name);\n    if (index !== -1) {\n      const updated = {\n        ...varieties[index],\n        colour\n      };\n      varieties.splice(index, 1, updated);\n    }\n    update.varieties = varieties;\n    dispatch(setPlant(update));\n\n    // Close the dropdown\n    setVarietyDropdownOpen(prev => ({\n      ...prev,\n      [variety.name]: false\n    }));\n  };\n  const handleMoveVariety = (existingVariety, movingVariety) => {\n    dispatch(moveVarietyAndSave({\n      plantId: plant._id,\n      existingVariety,\n      movingVariety\n    }));\n  };\n  const handleSaveClick = async () => {\n    const result = await dispatch(savePlant());\n    if (!result.error) {\n      navigate(routes.plants.path);\n    }\n  };\n  const handleDeleteClick = async () => {\n    const result = await dispatch(deletePlant());\n    if (!result.error) {\n      navigate(routes.plants.path);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container d-grid gap-3\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row sticky-top-navbar my-2 py-2 bg-white shadow\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-auto pt-3\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: routes.plants.path,\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: ['fat', 'chevron-left']\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 487,\n            columnNumber: 13\n          }, this), \"\\xA0 Back to Plants List\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 486,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 485,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"col\",\n        children: isNew ? 'New Plant' : plant.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 491,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 484,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"plant-size\",\n          children: \"Size\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 495,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"plant-size\",\n          value: plant.size,\n          onChange: handleSizeChange,\n          disabled: !canUpdate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 496,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 494,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"plant-crop\",\n          children: \"Crop\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 504,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"plant-crop\",\n          value: plant.crop,\n          onChange: handleCropChange,\n          disabled: !canUpdate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 505,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 503,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"plant-abbreviation\",\n          children: \"Abbreviation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 513,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"plant-abbreviation\",\n          value: plant.abbreviation,\n          onChange: handleAbbreviationChange,\n          disabled: !canUpdate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 514,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 512,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 493,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"plant-cuttings-per-pot\",\n          children: \"Cuttings per Pot\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 524,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"plant-cuttings-per-pot\",\n          type: \"number\",\n          value: plant.cuttingsPerPot,\n          onChange: handleCuttingsPerPotChange,\n          onFocus: handleFocus,\n          disabled: !canUpdate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 525,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 523,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"plant-pots-per-case\",\n          children: \"Pots Per Case\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 535,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"plant-pots-per-case\",\n          type: \"number\",\n          value: plant.potsPerCase,\n          onChange: handlePotsPerCaseChange,\n          onFocus: handleFocus,\n          disabled: !canUpdate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 536,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 534,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"plant-default-sticking-crew-size\",\n          children: \"Default Sticking Crew Size\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 546,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"plant-default-sticking-crew-size\",\n          type: \"number\",\n          value: plant.defaultStickingCrewSize,\n          onChange: handleDefaultStickingCrewSizeChange,\n          onFocus: handleFocus,\n          disabled: !canUpdate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 549,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 545,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 522,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"plant-cuttings-per-table-tight\",\n          children: \"Cuttings Per Table: Tight\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 561,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"plant-cuttings-per-table-tight\",\n          type: \"number\",\n          value: plant.cuttingsPerTableTight,\n          onChange: handleCuttingsPerTableTightChange,\n          onFocus: handleFocus,\n          disabled: !canUpdate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 564,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 560,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"plant-cuttings-per-table-partially-spaced\",\n          children: \"Cuttings Per Table: Partially Spaced\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 574,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"plant-cuttings-per-table-partially-spaced\",\n          type: \"number\",\n          value: plant.cuttingsPerTablePartiallySpaced,\n          onChange: handleCuttingsPerTablePartiallySpacedChange,\n          onFocus: handleFocus,\n          disabled: !canUpdate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 577,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 573,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"plant-cuttings-per-table-spaced\",\n          children: \"Cuttings Per Table: Spaced\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 587,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"plant-cuttings-per-table-spaced\",\n          type: \"number\",\n          value: plant.cuttingsPerTableSpaced,\n          onChange: handleCuttingsPerTableSpacedChange,\n          onFocus: handleFocus,\n          disabled: !canUpdate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 590,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 586,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 559,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"plant-sticking-cuttings-per-hour\",\n          children: \"Sticking: Cuttings per Hour\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 602,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"plant-sticking-cuttings-per-hour\",\n          type: \"number\",\n          value: plant.stickingCuttingsPerHour,\n          onChange: handleStickingCuttingsPerHourChange,\n          onFocus: handleFocus,\n          disabled: !canUpdate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 605,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 601,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"plant-spacing-pots-per-hour\",\n          children: \"Spacing: Pots per Hour\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 615,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"plant-spacing-pots-per-hour\",\n          type: \"number\",\n          value: plant.spacingPotsPerHour,\n          onChange: handleSpacingPotsPerHourChange,\n          onFocus: handleFocus,\n          disabled: !canUpdate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 618,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 614,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"plant-packing-cases-per-hour\",\n          children: \"Packing: Cases per Hour\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 628,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"plant-packing-cases-per-hour\",\n          type: \"number\",\n          value: plant.packingCasesPerHour,\n          onChange: handlePackingCasesPerHourChange,\n          onFocus: handleFocus,\n          disabled: !canUpdate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 631,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 627,\n        columnNumber: 9\n      }, this), !!plant.hasPinching && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"plant-pinching-pots-per-hour\",\n          children: \"Pinching: Pots per Hour\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 642,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"plant-pinching-pots-per-hour\",\n          type: \"number\",\n          value: plant.pinchingPotsPerHour,\n          onChange: handlePinchingingPotsPerHourChange,\n          onFocus: handleFocus,\n          disabled: !canUpdate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 645,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 641,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 600,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row my-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-3\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-check\",\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            id: \"plant-has-lights-out\",\n            type: \"checkbox\",\n            checked: plant.hasLightsOut,\n            onChange: handleHasLightsOutChange,\n            disabled: !canUpdate\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 659,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"plant-has-lights-out\",\n            children: \"Has Lights Out\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 666,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 658,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 657,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-check\",\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            id: \"plant-needs-pinching\",\n            type: \"checkbox\",\n            checked: plant.hasPinching,\n            onChange: handleHasPinchingChange,\n            disabled: !canUpdate\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 671,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"plant-needs-pinching\",\n            children: \"Needs Pinching\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 678,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 670,\n          columnNumber: 11\n        }, this), !!plant.hasPinching && /*#__PURE__*/_jsxDEV(FormGroup, {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"plant-pinching-pots-per-hour\",\n            children: \"Days after sticking to pinch\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 682,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            id: \"plant-pinching-pots-per-hour\",\n            type: \"number\",\n            value: plant.daysToPinch || '',\n            onChange: handleDaysToPinchChange,\n            onFocus: handleFocus,\n            disabled: !canUpdate\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 685,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 681,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 669,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"plant-colour\",\n          children: \"Colour\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 697,\n          columnNumber: 11\n        }, this), !!plant.colour && /*#__PURE__*/_jsxDEV(InputGroup, {\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            id: \"plant-colour\",\n            type: \"color\",\n            value: plant.colour || '',\n            onChange: handleColourChange,\n            disabled: !canUpdate\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 700,\n            columnNumber: 15\n          }, this), canUpdate && /*#__PURE__*/_jsxDEV(Button, {\n            size: \"sm\",\n            color: \"danger\",\n            outline: true,\n            onClick: handleClearColourClick,\n            children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n              icon: ['fat', 'trash']\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 713,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 708,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 699,\n          columnNumber: 13\n        }, this), !plant.colour && /*#__PURE__*/_jsxDEV(InputGroup, {\n          children: [/*#__PURE__*/_jsxDEV(InputGroupText, {\n            children: \"No Colour\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 720,\n            columnNumber: 15\n          }, this), canUpdate && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              id: \"add-colour\",\n              size: \"sm\",\n              color: \"success\",\n              outline: true,\n              onClick: handleAddColourClick,\n              children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: ['fat', 'palette']\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 729,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 723,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(UncontrolledTooltip, {\n              target: \"add-colour\",\n              children: \"Add Colour\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 731,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 719,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 696,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 656,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-3\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-check\",\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            id: \"plant-has-varieties\",\n            type: \"checkbox\",\n            checked: hasVarieties,\n            onChange: handleHasVarietiesChange,\n            disabled: !canUpdate\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 743,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"plant-has-varieties\",\n            children: \"Has Varieties\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 750,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 742,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 741,\n        columnNumber: 9\n      }, this), hasVarieties && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-6\",\n        children: [canUpdate && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-5\",\n            children: /*#__PURE__*/_jsxDEV(FormGroup, {\n              floating: true,\n              children: [/*#__PURE__*/_jsxDEV(Input, {\n                id: \"new-variety-name\",\n                value: newVariety,\n                onChange: handleNewVarietyChange,\n                onKeyUp: handleNewVarietyKeyUp,\n                placeholder: \"Add Variety\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 759,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Label, {\n                htmlFor: \"new-variety-name\",\n                children: \"Add Variety\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 766,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 758,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 757,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-4\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              outline: true,\n              color: \"success\",\n              onClick: handleAddNewVarietyClick,\n              disabled: !newVariety,\n              size: \"sm\",\n              children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: ['fat', 'plus']\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 776,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 770,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 769,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 756,\n          columnNumber: 15\n        }, this), (_plant$varieties = plant.varieties) === null || _plant$varieties === void 0 ? void 0 : _plant$varieties.map(variety => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-5\",\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              value: variety.name,\n              onChange: e => handleVarietyNameChange(e, variety),\n              disabled: !canUpdate\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 784,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 783,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-3\",\n            children: /*#__PURE__*/_jsxDEV(Dropdown, {\n              isOpen: varietyDropdownOpen[variety.name] || false,\n              toggle: () => toggleVarietyDropdown(variety.name),\n              disabled: !canUpdate,\n              children: [/*#__PURE__*/_jsxDEV(DropdownToggle, {\n                caret: true,\n                className: \"w-100 text-start d-flex align-items-center justify-content-between\",\n                style: {\n                  backgroundColor: 'white',\n                  borderColor: '#ced4da',\n                  color: '#495057'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex align-items-center\",\n                  children: variety.colour ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w16 h16 border rounded\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 806,\n                      columnNumber: 29\n                    }, this), variety.colour.name]\n                  }, void 0, true) : '---'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 803,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 795,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(DropdownMenu, {\n                className: \"w-100\",\n                children: [/*#__PURE__*/_jsxDEV(DropdownItem, {\n                  onClick: () => handleVarietyColourSelect(variety, ''),\n                  children: \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 817,\n                  columnNumber: 23\n                }, this), colours.map(c => /*#__PURE__*/_jsxDEV(DropdownItem, {\n                  onClick: () => handleVarietyColourSelect(variety, c.name),\n                  className: \"d-flex align-items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: '16px',\n                      height: '16px',\n                      backgroundColor: c.hex,\n                      border: '1px solid #ccc',\n                      marginRight: '8px',\n                      borderRadius: '2px'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 825,\n                    columnNumber: 27\n                  }, this), c.name]\n                }, c.name, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 821,\n                  columnNumber: 25\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 816,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 791,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 790,\n            columnNumber: 17\n          }, this), canUpdate && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-4\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              outline: true,\n              color: \"danger\",\n              onClick: () => handleDeleteVarietyClick(variety),\n              size: \"sm\",\n              children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: ['fat', 'trash-alt']\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 848,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 843,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 842,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 782,\n          columnNumber: 15\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 754,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 740,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row sticky-bottom bg-white border-top py-2\",\n      children: [!isNew && canDelete && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-auto\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleDeleteClick,\n          outline: true,\n          color: \"danger\",\n          size: \"lg\",\n          className: \"me-auto\",\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: ['fat', 'trash-alt']\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 866,\n            columnNumber: 15\n          }, this), \"\\xA0 Delete\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 860,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 859,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col text-end\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          tag: Link,\n          to: routes.plants.path,\n          outline: true,\n          size: \"lg\",\n          children: canUpdate ? 'Cancel' : 'Close'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 872,\n          columnNumber: 11\n        }, this), canUpdate && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [\"\\xA0\", /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSaveClick,\n            color: \"success\",\n            size: \"lg\",\n            children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n              icon: ['fat', 'save']\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 879,\n              columnNumber: 17\n            }, this), \"\\xA0 Save\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 878,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 871,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 857,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 483,\n    columnNumber: 5\n  }, this);\n}\n_s2(Detail, \"DUE4rbRCdXEfSMB7qMV7QjgdiAs=\", false, function () {\n  return [useDispatch, useNavigate, useAuth, useParams, useSelector, useSelector, useSelector];\n});\n_c2 = Detail;\nexport default Detail;\nvar _c, _c2;\n$RefreshReg$(_c, \"VarietyItem\");\n$RefreshReg$(_c2, \"Detail\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "useSelector", "useDispatch", "useParams", "useNavigate", "Link", "<PERSON><PERSON>", "FormGroup", "Input", "InputGroup", "InputGroupText", "Label", "UncontrolledTooltip", "Dropdown", "DropdownToggle", "DropdownMenu", "DropdownItem", "FontAwesomeIcon", "routes", "useAuth", "selectPlants", "moveVarietyAndSave", "deletePlant", "savePlant", "selectPlant", "setPlant", "createPlant", "handleFocus", "selectColours", "useDrag", "useDrop", "VarietyItem", "variety", "plant", "canUpdate", "colours", "varietyDropdownOpen", "onVarietyNameChange", "onVarietyColourSelect", "onDeleteVarietyClick", "onToggleVarietyDropdown", "onMoveVariety", "ref", "drag", "type", "item", "collect", "monitor", "isDragging", "drop", "accept", "droppedVariety", "isOver", "height", "name", "e", "backgroundColor", "borderColor", "color", "colour", "map", "c", "width", "hex", "border", "marginRight", "borderRadius", "Detail", "dispatch", "navigate", "isInRole", "id", "plants", "hasVarieties", "setHasVarieties", "newVariety", "setNewVariety", "setVarietyDropdownOpen", "isNew", "_rev", "canDelete", "found", "find", "p", "_id", "varieties", "cleanup", "handleSizeChange", "size", "target", "value", "crop", "update", "handleCropChange", "handleAbbreviationChange", "abbreviation", "handleCuttingsPerPotChange", "cuttingsPerPot", "valueAsNumber", "handleDefaultStickingCrewSizeChange", "defaultStickingCrewSize", "handleCuttingsPerTableTightChange", "cuttingsPerTableTight", "handleCuttingsPerTableSpacedChange", "cuttingsPerTableSpaced", "handleCuttingsPerTablePartiallySpacedChange", "cuttingsPerTablePartiallySpaced", "handlePotsPerCaseChange", "potsPerCase", "handleHasLightsOutChange", "hasLightsOut", "checked", "handleHasPinchingChange", "hasPinching", "pinchingPotsPerHour", "daysToPinch", "handleDaysToPinchChange", "handlePinchingingPotsPerHourChange", "handleColourChange", "handleClearColourClick", "handleAddColourClick", "handleHasVarietiesChange", "handleStickingCuttingsPerHourChange", "stickingCuttingsPerHour", "handleSpacingPotsPerHourChange", "spacingPotsPerHour", "handlePackingCasesPerHourChange", "packingCasesPerHour", "handleNewVarietyChange", "handleNewVarietyKeyUp", "key", "handleAddNewVarietyClick", "v", "push", "stickingSortOrder", "handleVarietyNameChange", "index", "findIndex", "updated", "splice", "handleDeleteVarietyClick", "toggleVarietyDropdown", "varietyName", "prev", "handleVarietyColourSelect", "colourName", "handleMoveVariety", "existingVariety", "movingVariety", "plantId", "handleSaveClick", "result", "error", "path", "handleDeleteClick"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/plants/Detail.tsx"], "sourcesContent": ["import React, { useEffect, useState, useRef } from 'react';\r\nimport { useSelector, useDispatch } from 'react-redux';\r\nimport { useParams, useNavigate } from 'react-router';\r\nimport { Link } from 'react-router-dom';\r\nimport {\r\n  Button,\r\n  FormGroup,\r\n  Input,\r\n  InputGroup,\r\n  InputGroupText,\r\n  Label,\r\n  UncontrolledTooltip,\r\n  Dropdown,\r\n  DropdownToggle,\r\n  DropdownMenu,\r\n  DropdownItem,\r\n} from 'reactstrap';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { routes } from 'app/routes';\r\nimport { useAuth } from 'features/auth/use-auth';\r\nimport { selectPlants, generateAndSaveVarietySortOrder, moveVarietyAndSave, sortVariety } from './plants-slice';\r\nimport { deletePlant, savePlant, selectPlant, setPlant } from './detail-slice';\r\nimport { createPlant, Variety } from 'api/models/plants';\r\nimport { handleFocus } from 'utils/focus';\r\nimport { selectColours } from 'features/colours/colours-slice';\r\nimport { DndProvider, useDrag, useDrop } from 'react-dnd';\r\nimport { HTML5Backend } from 'react-dnd-html5-backend';\r\n\r\ninterface VarietyItemProps {\r\n  variety: Variety;\r\n  plant: any;\r\n  canUpdate: boolean;\r\n  colours: any[];\r\n  varietyDropdownOpen: {[key: string]: boolean};\r\n  onVarietyNameChange: (e: React.ChangeEvent<HTMLInputElement>, variety: Variety) => void;\r\n  onVarietyColourSelect: (variety: Variety, colourName: string) => void;\r\n  onDeleteVarietyClick: (variety: Variety) => void;\r\n  onToggleVarietyDropdown: (varietyName: string) => void;\r\n  onMoveVariety: (existingVariety: Variety, movingVariety: Variety) => void;\r\n}\r\n\r\nfunction VarietyItem({\r\n  variety,\r\n  plant,\r\n  canUpdate,\r\n  colours,\r\n  varietyDropdownOpen,\r\n  onVarietyNameChange,\r\n  onVarietyColourSelect,\r\n  onDeleteVarietyClick,\r\n  onToggleVarietyDropdown,\r\n  onMoveVariety\r\n}: VarietyItemProps) {\r\n  const ref = useRef<HTMLDivElement>(null);\r\n\r\n  const [, drag] = useDrag(() => ({\r\n    type: 'variety',\r\n    item: variety,\r\n    collect: (monitor) => ({\r\n      isDragging: monitor.isDragging(),\r\n    }),\r\n  }));\r\n\r\n  const [, drop] = useDrop(() => ({\r\n    accept: 'variety',\r\n    drop: (droppedVariety: Variety) => {\r\n      onMoveVariety(variety, droppedVariety);\r\n    },\r\n    collect: (monitor) => ({\r\n      isOver: monitor.isOver(),\r\n    }),\r\n  }));\r\n\r\n  drag(drop(ref));\r\n\r\n  return (\r\n    <div className=\"row\" ref={ref}>\r\n      {canUpdate && (\r\n        <div className=\"col-1\">\r\n          <div\r\n            className=\"cursor-move d-flex align-items-center justify-content-center\"\r\n            style={{ height: '38px' }}>\r\n            <FontAwesomeIcon icon={['fat', 'grip-vertical']} />\r\n          </div>\r\n        </div>\r\n      )}\r\n      <div className={canUpdate ? \"col-4\" : \"col-5\"}>\r\n        <Input\r\n          value={variety.name}\r\n          onChange={(e) => onVarietyNameChange(e, variety)}\r\n          disabled={!canUpdate}\r\n        />\r\n      </div>\r\n      <div className=\"col-3\">\r\n        <Dropdown\r\n          isOpen={varietyDropdownOpen[variety.name] || false}\r\n          toggle={() => onToggleVarietyDropdown(variety.name)}\r\n          disabled={!canUpdate}>\r\n          <DropdownToggle\r\n            caret\r\n            className=\"w-100 text-start d-flex align-items-center justify-content-between\"\r\n            style={{\r\n              backgroundColor: 'white',\r\n              borderColor: '#ced4da',\r\n              color: '#495057'\r\n            }}>\r\n            <div className=\"d-flex align-items-center\">\r\n              {variety.colour ? (\r\n                <>\r\n                  <div\r\n                    className='w16 h16 border rounded'\r\n                  />\r\n                  {variety.colour.name}\r\n                </>\r\n              ) : (\r\n                '---'\r\n              )}\r\n            </div>\r\n          </DropdownToggle>\r\n          <DropdownMenu className=\"w-100\">\r\n            <DropdownItem onClick={() => onVarietyColourSelect(variety, '')}>\r\n              ---\r\n            </DropdownItem>\r\n            {colours.map((c) => (\r\n              <DropdownItem\r\n                key={c.name}\r\n                onClick={() => onVarietyColourSelect(variety, c.name)}\r\n                className=\"d-flex align-items-center\">\r\n                <div\r\n                  style={{\r\n                    width: '16px',\r\n                    height: '16px',\r\n                    backgroundColor: c.hex,\r\n                    border: '1px solid #ccc',\r\n                    marginRight: '8px',\r\n                    borderRadius: '2px'\r\n                  }}\r\n                />\r\n                {c.name}\r\n              </DropdownItem>\r\n            ))}\r\n          </DropdownMenu>\r\n        </Dropdown>\r\n      </div>\r\n      {canUpdate && (\r\n        <div className=\"col-4\">\r\n          <Button\r\n            outline\r\n            color=\"danger\"\r\n            onClick={() => onDeleteVarietyClick(variety)}\r\n            size=\"sm\">\r\n            <FontAwesomeIcon icon={['fat', 'trash-alt']} />\r\n          </Button>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport function Detail() {\r\n  const dispatch = useDispatch(),\r\n    navigate = useNavigate(),\r\n    { isInRole } = useAuth(),\r\n    { id } = useParams<{ id: string }>(),\r\n    plants = useSelector(selectPlants),\r\n    plant = useSelector(selectPlant),\r\n    colours = useSelector(selectColours),\r\n    [hasVarieties, setHasVarieties] = useState(false),\r\n    [newVariety, setNewVariety] = useState(''),\r\n    [varietyDropdownOpen, setVarietyDropdownOpen] = useState<{[key: string]: boolean}>({}),\r\n    isNew = !plant._rev,\r\n    canUpdate =\r\n      (isNew && isInRole('create:plants')) || isInRole('update:plants'),\r\n    canDelete = isInRole('delete:plants');\r\n\r\n  useEffect(() => {\r\n    const found = plants.find((p) => p._id === id);\r\n    if (found && found._id !== plant._id) {\r\n      dispatch(setPlant(found));\r\n      setHasVarieties(!!found.varieties);\r\n      setNewVariety('');\r\n    } else if (id === 'new' && plant._rev) {\r\n      dispatch(setPlant(createPlant()));\r\n      setHasVarieties(false);\r\n      setNewVariety('');\r\n    }\r\n  }, [dispatch, id, plant, plants]);\r\n\r\n  useEffect(() => {\r\n    return function cleanup() {\r\n      dispatch(setPlant(createPlant()));\r\n    };\r\n  }, [dispatch]);\r\n\r\n  const handleSizeChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const size = e.target.value,\r\n      name = `${size} ${plant.crop}`,\r\n      update = { ...plant, size, name };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleCropChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const crop = e.target.value,\r\n      name = `${plant.size} ${crop}`,\r\n      update = { ...plant, crop, name };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleAbbreviationChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const abbreviation = e.target.value,\r\n      update = { ...plant, abbreviation };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleCuttingsPerPotChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    const cuttingsPerPot = e.target.valueAsNumber,\r\n      update = { ...plant, cuttingsPerPot };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleDefaultStickingCrewSizeChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    const defaultStickingCrewSize = e.target.valueAsNumber,\r\n      update = { ...plant, defaultStickingCrewSize };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleCuttingsPerTableTightChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    const cuttingsPerTableTight = e.target.valueAsNumber,\r\n      update = { ...plant, cuttingsPerTableTight };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleCuttingsPerTableSpacedChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    const cuttingsPerTableSpaced = e.target.valueAsNumber,\r\n      update = { ...plant, cuttingsPerTableSpaced };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleCuttingsPerTablePartiallySpacedChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    const cuttingsPerTablePartiallySpaced = e.target.valueAsNumber,\r\n      update = { ...plant, cuttingsPerTablePartiallySpaced };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handlePotsPerCaseChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const potsPerCase = e.target.valueAsNumber,\r\n      update = { ...plant, potsPerCase };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleHasLightsOutChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const hasLightsOut = e.target.checked,\r\n      update = { ...plant, hasLightsOut };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleHasPinchingChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const hasPinching = e.target.checked,\r\n      update = { ...plant, hasPinching };\r\n\r\n    if (!hasPinching) {\r\n      update.pinchingPotsPerHour = 0;\r\n      delete update.daysToPinch;\r\n    } else {\r\n      if (!update.daysToPinch) {\r\n        update.daysToPinch = 1;\r\n      }\r\n    }\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleDaysToPinchChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const daysToPinch = e.target.valueAsNumber || 0,\r\n      update = { ...plant, daysToPinch };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handlePinchingingPotsPerHourChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    const pinchingPotsPerHour = e.target.valueAsNumber,\r\n      update = { ...plant, pinchingPotsPerHour };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleColourChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const colour = e.target.value,\r\n      update = { ...plant };\r\n\r\n    if (colour) {\r\n      update.colour = colour;\r\n    } else {\r\n      delete plant.colour;\r\n    }\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleClearColourClick = () => {\r\n    const update = { ...plant };\r\n    delete update.colour;\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleAddColourClick = () => {\r\n    const update = { ...plant, colour: '#ffffff' };\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleHasVarietiesChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const hasVarieties = e.target.checked,\r\n      update = { ...plant };\r\n\r\n    if (hasVarieties) {\r\n      update.varieties = [];\r\n    } else {\r\n      delete update.varieties;\r\n    }\r\n\r\n    dispatch(setPlant(update));\r\n\r\n    setHasVarieties(hasVarieties);\r\n  };\r\n\r\n  const handleStickingCuttingsPerHourChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    const stickingCuttingsPerHour = e.target.valueAsNumber,\r\n      update = { ...plant, stickingCuttingsPerHour };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleSpacingPotsPerHourChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    const spacingPotsPerHour = e.target.valueAsNumber,\r\n      update = { ...plant, spacingPotsPerHour };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handlePackingCasesPerHourChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    const packingCasesPerHour = e.target.valueAsNumber,\r\n      update = { ...plant, packingCasesPerHour };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleNewVarietyChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const newVariety = e.target.value;\r\n    setNewVariety(newVariety);\r\n  };\r\n\r\n  const handleNewVarietyKeyUp = (e: React.KeyboardEvent) => {\r\n    if (newVariety && e.key === 'Enter') {\r\n      handleAddNewVarietyClick();\r\n    }\r\n  };\r\n\r\n  const handleAddNewVarietyClick = () => {\r\n    if (newVariety) {\r\n      const update = { ...plant },\r\n        varieties = (update.varieties || []).map((v) => ({ ...v }));\r\n\r\n      varieties.push({ name: newVariety, stickingSortOrder: null });\r\n\r\n      update.varieties = varieties;\r\n      dispatch(setPlant(update));\r\n      setNewVariety('');\r\n    }\r\n  };\r\n\r\n  const handleVarietyNameChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>,\r\n    variety: Variety\r\n  ) => {\r\n    const update = { ...plant },\r\n      varieties = (update.varieties || []).map((v) => ({ ...v })),\r\n      index = varieties.findIndex((v) => v.name === variety.name);\r\n\r\n    if (index !== -1) {\r\n      const name = e.target.value,\r\n        updated = { ...varieties[index], name };\r\n      varieties.splice(index, 1, updated);\r\n    }\r\n\r\n    update.varieties = varieties;\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n\r\n\r\n  const handleDeleteVarietyClick = (variety: Variety) => {\r\n    const update = { ...plant },\r\n      varieties = (update.varieties || []).map((v) => ({ ...v })),\r\n      index = varieties.findIndex((v) => v.name === variety.name);\r\n\r\n    if (index !== -1) {\r\n      varieties.splice(index, 1);\r\n    }\r\n\r\n    update.varieties = varieties;\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const toggleVarietyDropdown = (varietyName: string) => {\r\n    setVarietyDropdownOpen(prev => ({\r\n      ...prev,\r\n      [varietyName]: !prev[varietyName]\r\n    }));\r\n  };\r\n\r\n  const handleVarietyColourSelect = (variety: Variety, colourName: string) => {\r\n    const colour = colourName ? colours.find((c) => c.name === colourName) || null : null;\r\n    const update = { ...plant },\r\n      varieties = (update.varieties || []).map((v) => ({ ...v })),\r\n      index = varieties.findIndex((v) => v.name === variety.name);\r\n\r\n    if (index !== -1) {\r\n      const updated = { ...varieties[index], colour };\r\n      varieties.splice(index, 1, updated);\r\n    }\r\n\r\n    update.varieties = varieties;\r\n    dispatch(setPlant(update));\r\n\r\n    // Close the dropdown\r\n    setVarietyDropdownOpen(prev => ({\r\n      ...prev,\r\n      [variety.name]: false\r\n    }));\r\n  };\r\n\r\n  const handleMoveVariety = (existingVariety: Variety, movingVariety: Variety) => {\r\n    dispatch(moveVarietyAndSave({ plantId: plant._id, existingVariety, movingVariety }));\r\n  };\r\n\r\n  const handleSaveClick = async () => {\r\n    const result: any = await dispatch(savePlant());\r\n\r\n    if (!result.error) {\r\n      navigate(routes.plants.path);\r\n    }\r\n  };\r\n\r\n  const handleDeleteClick = async () => {\r\n    const result: any = await dispatch(deletePlant());\r\n\r\n    if (!result.error) {\r\n      navigate(routes.plants.path);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"container d-grid gap-3\">\r\n      <div className=\"row sticky-top-navbar my-2 py-2 bg-white shadow\">\r\n        <div className=\"col-auto pt-3\">\r\n          <Link to={routes.plants.path}>\r\n            <FontAwesomeIcon icon={['fat', 'chevron-left']} />\r\n            &nbsp; Back to Plants List\r\n          </Link>\r\n        </div>\r\n        <h1 className=\"col\">{isNew ? 'New Plant' : plant.name}</h1>\r\n      </div>\r\n      <div className=\"row\">\r\n        <div className=\"col-12 col-md-3\">\r\n          <label htmlFor=\"plant-size\">Size</label>\r\n          <Input\r\n            id=\"plant-size\"\r\n            value={plant.size}\r\n            onChange={handleSizeChange}\r\n            disabled={!canUpdate}\r\n          />\r\n        </div>\r\n        <div className=\"col-12 col-md-3\">\r\n          <label htmlFor=\"plant-crop\">Crop</label>\r\n          <Input\r\n            id=\"plant-crop\"\r\n            value={plant.crop}\r\n            onChange={handleCropChange}\r\n            disabled={!canUpdate}\r\n          />\r\n        </div>\r\n        <div className=\"col-12 col-md-3\">\r\n          <label htmlFor=\"plant-abbreviation\">Abbreviation</label>\r\n          <Input\r\n            id=\"plant-abbreviation\"\r\n            value={plant.abbreviation}\r\n            onChange={handleAbbreviationChange}\r\n            disabled={!canUpdate}\r\n          />\r\n        </div>\r\n      </div>\r\n      <div className=\"row\">\r\n        <div className=\"col-12 col-md-3\">\r\n          <label htmlFor=\"plant-cuttings-per-pot\">Cuttings per Pot</label>\r\n          <Input\r\n            id=\"plant-cuttings-per-pot\"\r\n            type=\"number\"\r\n            value={plant.cuttingsPerPot}\r\n            onChange={handleCuttingsPerPotChange}\r\n            onFocus={handleFocus}\r\n            disabled={!canUpdate}\r\n          />\r\n        </div>\r\n        <div className=\"col-12 col-md-3\">\r\n          <label htmlFor=\"plant-pots-per-case\">Pots Per Case</label>\r\n          <Input\r\n            id=\"plant-pots-per-case\"\r\n            type=\"number\"\r\n            value={plant.potsPerCase}\r\n            onChange={handlePotsPerCaseChange}\r\n            onFocus={handleFocus}\r\n            disabled={!canUpdate}\r\n          />\r\n        </div>\r\n        <div className=\"col-12 col-md-3\">\r\n          <label htmlFor=\"plant-default-sticking-crew-size\">\r\n            Default Sticking Crew Size\r\n          </label>\r\n          <Input\r\n            id=\"plant-default-sticking-crew-size\"\r\n            type=\"number\"\r\n            value={plant.defaultStickingCrewSize}\r\n            onChange={handleDefaultStickingCrewSizeChange}\r\n            onFocus={handleFocus}\r\n            disabled={!canUpdate}\r\n          />\r\n        </div>\r\n      </div>\r\n      <div className=\"row\">\r\n        <div className=\"col-12 col-md-3\">\r\n          <label htmlFor=\"plant-cuttings-per-table-tight\">\r\n            Cuttings Per Table: Tight\r\n          </label>\r\n          <Input\r\n            id=\"plant-cuttings-per-table-tight\"\r\n            type=\"number\"\r\n            value={plant.cuttingsPerTableTight}\r\n            onChange={handleCuttingsPerTableTightChange}\r\n            onFocus={handleFocus}\r\n            disabled={!canUpdate}\r\n          />\r\n        </div>\r\n        <div className=\"col-12 col-md-3\">\r\n          <label htmlFor=\"plant-cuttings-per-table-partially-spaced\">\r\n            Cuttings Per Table: Partially Spaced\r\n          </label>\r\n          <Input\r\n            id=\"plant-cuttings-per-table-partially-spaced\"\r\n            type=\"number\"\r\n            value={plant.cuttingsPerTablePartiallySpaced}\r\n            onChange={handleCuttingsPerTablePartiallySpacedChange}\r\n            onFocus={handleFocus}\r\n            disabled={!canUpdate}\r\n          />\r\n        </div>\r\n        <div className=\"col-12 col-md-3\">\r\n          <label htmlFor=\"plant-cuttings-per-table-spaced\">\r\n            Cuttings Per Table: Spaced\r\n          </label>\r\n          <Input\r\n            id=\"plant-cuttings-per-table-spaced\"\r\n            type=\"number\"\r\n            value={plant.cuttingsPerTableSpaced}\r\n            onChange={handleCuttingsPerTableSpacedChange}\r\n            onFocus={handleFocus}\r\n            disabled={!canUpdate}\r\n          />\r\n        </div>\r\n      </div>\r\n      <div className=\"row\">\r\n        <div className=\"col-12 col-md-3\">\r\n          <label htmlFor=\"plant-sticking-cuttings-per-hour\">\r\n            Sticking: Cuttings per Hour\r\n          </label>\r\n          <Input\r\n            id=\"plant-sticking-cuttings-per-hour\"\r\n            type=\"number\"\r\n            value={plant.stickingCuttingsPerHour}\r\n            onChange={handleStickingCuttingsPerHourChange}\r\n            onFocus={handleFocus}\r\n            disabled={!canUpdate}\r\n          />\r\n        </div>\r\n        <div className=\"col-12 col-md-3\">\r\n          <label htmlFor=\"plant-spacing-pots-per-hour\">\r\n            Spacing: Pots per Hour\r\n          </label>\r\n          <Input\r\n            id=\"plant-spacing-pots-per-hour\"\r\n            type=\"number\"\r\n            value={plant.spacingPotsPerHour}\r\n            onChange={handleSpacingPotsPerHourChange}\r\n            onFocus={handleFocus}\r\n            disabled={!canUpdate}\r\n          />\r\n        </div>\r\n        <div className=\"col-12 col-md-3\">\r\n          <label htmlFor=\"plant-packing-cases-per-hour\">\r\n            Packing: Cases per Hour\r\n          </label>\r\n          <Input\r\n            id=\"plant-packing-cases-per-hour\"\r\n            type=\"number\"\r\n            value={plant.packingCasesPerHour}\r\n            onChange={handlePackingCasesPerHourChange}\r\n            onFocus={handleFocus}\r\n            disabled={!canUpdate}\r\n          />\r\n        </div>\r\n        {!!plant.hasPinching && (\r\n          <div className=\"col-12 col-md-3\">\r\n            <label htmlFor=\"plant-pinching-pots-per-hour\">\r\n              Pinching: Pots per Hour\r\n            </label>\r\n            <Input\r\n              id=\"plant-pinching-pots-per-hour\"\r\n              type=\"number\"\r\n              value={plant.pinchingPotsPerHour}\r\n              onChange={handlePinchingingPotsPerHourChange}\r\n              onFocus={handleFocus}\r\n              disabled={!canUpdate}\r\n            />\r\n          </div>\r\n        )}\r\n      </div>\r\n      <div className=\"row my-3\">\r\n        <div className=\"col-12 col-md-3\">\r\n          <div className=\"form-check\">\r\n            <Input\r\n              id=\"plant-has-lights-out\"\r\n              type=\"checkbox\"\r\n              checked={plant.hasLightsOut}\r\n              onChange={handleHasLightsOutChange}\r\n              disabled={!canUpdate}\r\n            />\r\n            <label htmlFor=\"plant-has-lights-out\">Has Lights Out</label>\r\n          </div>\r\n        </div>\r\n        <div className=\"col-12 col-md-3\">\r\n          <div className=\"form-check\">\r\n            <Input\r\n              id=\"plant-needs-pinching\"\r\n              type=\"checkbox\"\r\n              checked={plant.hasPinching}\r\n              onChange={handleHasPinchingChange}\r\n              disabled={!canUpdate}\r\n            />\r\n            <label htmlFor=\"plant-needs-pinching\">Needs Pinching</label>\r\n          </div>\r\n          {!!plant.hasPinching && (\r\n            <FormGroup>\r\n              <label htmlFor=\"plant-pinching-pots-per-hour\">\r\n                Days after sticking to pinch\r\n              </label>\r\n              <Input\r\n                id=\"plant-pinching-pots-per-hour\"\r\n                type=\"number\"\r\n                value={plant.daysToPinch || ''}\r\n                onChange={handleDaysToPinchChange}\r\n                onFocus={handleFocus}\r\n                disabled={!canUpdate}\r\n              />\r\n            </FormGroup>\r\n          )}\r\n        </div>\r\n        <div className=\"col-12 col-md-3\">\r\n          <label htmlFor=\"plant-colour\">Colour</label>\r\n          {!!plant.colour && (\r\n            <InputGroup>\r\n              <Input\r\n                id=\"plant-colour\"\r\n                type=\"color\"\r\n                value={plant.colour || ''}\r\n                onChange={handleColourChange}\r\n                disabled={!canUpdate}\r\n              />\r\n              {canUpdate && (\r\n                <Button\r\n                  size=\"sm\"\r\n                  color=\"danger\"\r\n                  outline\r\n                  onClick={handleClearColourClick}>\r\n                  <FontAwesomeIcon icon={['fat', 'trash']} />\r\n                </Button>\r\n              )}\r\n            </InputGroup>\r\n          )}\r\n          {!plant.colour && (\r\n            <InputGroup>\r\n              <InputGroupText>No Colour</InputGroupText>\r\n              {canUpdate && (\r\n                <>\r\n                  <Button\r\n                    id=\"add-colour\"\r\n                    size=\"sm\"\r\n                    color=\"success\"\r\n                    outline\r\n                    onClick={handleAddColourClick}>\r\n                    <FontAwesomeIcon icon={['fat', 'palette']} />\r\n                  </Button>\r\n                  <UncontrolledTooltip target=\"add-colour\">\r\n                    Add Colour\r\n                  </UncontrolledTooltip>\r\n                </>\r\n              )}\r\n            </InputGroup>\r\n          )}\r\n        </div>\r\n      </div>\r\n      <div className=\"row\">\r\n        <div className=\"col-12 col-md-3\">\r\n          <div className=\"form-check\">\r\n            <Input\r\n              id=\"plant-has-varieties\"\r\n              type=\"checkbox\"\r\n              checked={hasVarieties}\r\n              onChange={handleHasVarietiesChange}\r\n              disabled={!canUpdate}\r\n            />\r\n            <label htmlFor=\"plant-has-varieties\">Has Varieties</label>\r\n          </div>\r\n        </div>\r\n        {hasVarieties && (\r\n          <div className=\"col-6\">\r\n            {canUpdate && (\r\n              <div className=\"row\">\r\n                <div className=\"col-5\">\r\n                  <FormGroup floating>\r\n                    <Input\r\n                      id=\"new-variety-name\"\r\n                      value={newVariety}\r\n                      onChange={handleNewVarietyChange}\r\n                      onKeyUp={handleNewVarietyKeyUp}\r\n                      placeholder=\"Add Variety\"\r\n                    />\r\n                    <Label htmlFor=\"new-variety-name\">Add Variety</Label>\r\n                  </FormGroup>\r\n                </div>\r\n                <div className=\"col-4\">\r\n                  <Button\r\n                    outline\r\n                    color=\"success\"\r\n                    onClick={handleAddNewVarietyClick}\r\n                    disabled={!newVariety}\r\n                    size=\"sm\">\r\n                    <FontAwesomeIcon icon={['fat', 'plus']} />\r\n                  </Button>\r\n                </div>\r\n              </div>\r\n            )}\r\n            {plant.varieties?.map((variety) => (\r\n              <div className=\"row\">\r\n                <div className=\"col-5\">\r\n                  <Input\r\n                    value={variety.name}\r\n                    onChange={(e) => handleVarietyNameChange(e, variety)}\r\n                    disabled={!canUpdate}\r\n                  />\r\n                </div>\r\n                <div className=\"col-3\">\r\n                  <Dropdown\r\n                    isOpen={varietyDropdownOpen[variety.name] || false}\r\n                    toggle={() => toggleVarietyDropdown(variety.name)}\r\n                    disabled={!canUpdate}>\r\n                    <DropdownToggle\r\n                      caret\r\n                      className=\"w-100 text-start d-flex align-items-center justify-content-between\"\r\n                      style={{\r\n                        backgroundColor: 'white',\r\n                        borderColor: '#ced4da',\r\n                        color: '#495057'\r\n                      }}>\r\n                      <div className=\"d-flex align-items-center\">\r\n                        {variety.colour ? (\r\n                          <>\r\n                            <div\r\n                              className='w16 h16 border rounded'\r\n                            />\r\n                            {variety.colour.name}\r\n                          </>\r\n                        ) : (\r\n                          '---'\r\n                        )}\r\n                      </div>\r\n                    </DropdownToggle>\r\n                    <DropdownMenu className=\"w-100\">\r\n                      <DropdownItem onClick={() => handleVarietyColourSelect(variety, '')}>\r\n                        ---\r\n                      </DropdownItem>\r\n                      {colours.map((c) => (\r\n                        <DropdownItem\r\n                          key={c.name}\r\n                          onClick={() => handleVarietyColourSelect(variety, c.name)}\r\n                          className=\"d-flex align-items-center\">\r\n                          <div\r\n                            style={{\r\n                              width: '16px',\r\n                              height: '16px',\r\n                              backgroundColor: c.hex,\r\n                              border: '1px solid #ccc',\r\n                              marginRight: '8px',\r\n                              borderRadius: '2px'\r\n                            }}\r\n                          />\r\n                          {c.name}\r\n                        </DropdownItem>\r\n                      ))}\r\n                    </DropdownMenu>\r\n                  </Dropdown>\r\n                </div>\r\n                {canUpdate && (\r\n                  <div className=\"col-4\">\r\n                    <Button\r\n                      outline\r\n                      color=\"danger\"\r\n                      onClick={() => handleDeleteVarietyClick(variety)}\r\n                      size=\"sm\">\r\n                      <FontAwesomeIcon icon={['fat', 'trash-alt']} />\r\n                    </Button>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            ))}\r\n          </div>\r\n        )}\r\n      </div>\r\n      <div className=\"row sticky-bottom bg-white border-top py-2\">\r\n        {!isNew && canDelete && (\r\n          <div className=\"col-auto\">\r\n            <Button\r\n              onClick={handleDeleteClick}\r\n              outline\r\n              color=\"danger\"\r\n              size=\"lg\"\r\n              className=\"me-auto\">\r\n              <FontAwesomeIcon icon={['fat', 'trash-alt']} />\r\n              &nbsp; Delete\r\n            </Button>\r\n          </div>\r\n        )}\r\n        <div className=\"col text-end\">\r\n          <Button tag={Link} to={routes.plants.path} outline size=\"lg\">\r\n            {canUpdate ? 'Cancel' : 'Close'}\r\n          </Button>\r\n          {canUpdate && (\r\n            <>\r\n              &nbsp;\r\n              <Button onClick={handleSaveClick} color=\"success\" size=\"lg\">\r\n                <FontAwesomeIcon icon={['fat', 'save']} />\r\n                &nbsp; Save\r\n              </Button>\r\n            </>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Detail;\r\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,SAAS,EAAEC,WAAW,QAAQ,cAAc;AACrD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SACEC,MAAM,EACNC,SAAS,EACTC,KAAK,EACLC,UAAU,EACVC,cAAc,EACdC,KAAK,EACLC,mBAAmB,EACnBC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,YAAY,QACP,YAAY;AACnB,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,MAAM,QAAQ,YAAY;AACnC,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,YAAY,EAAmCC,kBAAkB,QAAqB,gBAAgB;AAC/G,SAASC,WAAW,EAAEC,SAAS,EAAEC,WAAW,EAAEC,QAAQ,QAAQ,gBAAgB;AAC9E,SAASC,WAAW,QAAiB,mBAAmB;AACxD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,aAAa,QAAQ,gCAAgC;AAC9D,SAAsBC,OAAO,EAAEC,OAAO,QAAQ,WAAW;AAAC;AAAA;AAgB1D,SAASC,WAAW,OAWC;EAAA;EAAA,IAXA;IACnBC,OAAO;IACPC,KAAK;IACLC,SAAS;IACTC,OAAO;IACPC,mBAAmB;IACnBC,mBAAmB;IACnBC,qBAAqB;IACrBC,oBAAoB;IACpBC,uBAAuB;IACvBC;EACgB,CAAC;EACjB,MAAMC,GAAG,GAAG1C,MAAM,CAAiB,IAAI,CAAC;EAExC,MAAM,GAAG2C,IAAI,CAAC,GAAGd,OAAO,CAAC,OAAO;IAC9Be,IAAI,EAAE,SAAS;IACfC,IAAI,EAAEb,OAAO;IACbc,OAAO,EAAGC,OAAO,KAAM;MACrBC,UAAU,EAAED,OAAO,CAACC,UAAU;IAChC,CAAC;EACH,CAAC,CAAC,CAAC;EAEH,MAAM,GAAGC,IAAI,CAAC,GAAGnB,OAAO,CAAC,OAAO;IAC9BoB,MAAM,EAAE,SAAS;IACjBD,IAAI,EAAGE,cAAuB,IAAK;MACjCV,aAAa,CAACT,OAAO,EAAEmB,cAAc,CAAC;IACxC,CAAC;IACDL,OAAO,EAAGC,OAAO,KAAM;MACrBK,MAAM,EAAEL,OAAO,CAACK,MAAM;IACxB,CAAC;EACH,CAAC,CAAC,CAAC;EAEHT,IAAI,CAACM,IAAI,CAACP,GAAG,CAAC,CAAC;EAEf,oBACE;IAAK,SAAS,EAAC,KAAK;IAAC,GAAG,EAAEA,GAAI;IAAA,WAC3BR,SAAS,iBACR;MAAK,SAAS,EAAC,OAAO;MAAA,uBACpB;QACE,SAAS,EAAC,8DAA8D;QACxE,KAAK,EAAE;UAAEmB,MAAM,EAAE;QAAO,CAAE;QAAA,uBAC1B,QAAC,eAAe;UAAC,IAAI,EAAE,CAAC,KAAK,EAAE,eAAe;QAAE;UAAA;UAAA;UAAA;QAAA;MAAG;QAAA;QAAA;QAAA;MAAA;IAC/C;MAAA;MAAA;MAAA;IAAA,QAET,eACD;MAAK,SAAS,EAAEnB,SAAS,GAAG,OAAO,GAAG,OAAQ;MAAA,uBAC5C,QAAC,KAAK;QACJ,KAAK,EAAEF,OAAO,CAACsB,IAAK;QACpB,QAAQ,EAAGC,CAAC,IAAKlB,mBAAmB,CAACkB,CAAC,EAAEvB,OAAO,CAAE;QACjD,QAAQ,EAAE,CAACE;MAAU;QAAA;QAAA;QAAA;MAAA;IACrB;MAAA;MAAA;MAAA;IAAA,QACE,eACN;MAAK,SAAS,EAAC,OAAO;MAAA,uBACpB,QAAC,QAAQ;QACP,MAAM,EAAEE,mBAAmB,CAACJ,OAAO,CAACsB,IAAI,CAAC,IAAI,KAAM;QACnD,MAAM,EAAE,MAAMd,uBAAuB,CAACR,OAAO,CAACsB,IAAI,CAAE;QACpD,QAAQ,EAAE,CAACpB,SAAU;QAAA,wBACrB,QAAC,cAAc;UACb,KAAK;UACL,SAAS,EAAC,oEAAoE;UAC9E,KAAK,EAAE;YACLsB,eAAe,EAAE,OAAO;YACxBC,WAAW,EAAE,SAAS;YACtBC,KAAK,EAAE;UACT,CAAE;UAAA,uBACF;YAAK,SAAS,EAAC,2BAA2B;YAAA,UACvC1B,OAAO,CAAC2B,MAAM,gBACb;cAAA,wBACE;gBACE,SAAS,EAAC;cAAwB;gBAAA;gBAAA;gBAAA;cAAA,QAClC,EACD3B,OAAO,CAAC2B,MAAM,CAACL,IAAI;YAAA,gBACnB,GAEH;UACD;YAAA;YAAA;YAAA;UAAA;QACG;UAAA;UAAA;UAAA;QAAA,QACS,eACjB,QAAC,YAAY;UAAC,SAAS,EAAC,OAAO;UAAA,wBAC7B,QAAC,YAAY;YAAC,OAAO,EAAE,MAAMhB,qBAAqB,CAACN,OAAO,EAAE,EAAE,CAAE;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAEjD,EACdG,OAAO,CAACyB,GAAG,CAAEC,CAAC,iBACb,QAAC,YAAY;YAEX,OAAO,EAAE,MAAMvB,qBAAqB,CAACN,OAAO,EAAE6B,CAAC,CAACP,IAAI,CAAE;YACtD,SAAS,EAAC,2BAA2B;YAAA,wBACrC;cACE,KAAK,EAAE;gBACLQ,KAAK,EAAE,MAAM;gBACbT,MAAM,EAAE,MAAM;gBACdG,eAAe,EAAEK,CAAC,CAACE,GAAG;gBACtBC,MAAM,EAAE,gBAAgB;gBACxBC,WAAW,EAAE,KAAK;gBAClBC,YAAY,EAAE;cAChB;YAAE;cAAA;cAAA;cAAA;YAAA,QACF,EACDL,CAAC,CAACP,IAAI;UAAA,GAbFO,CAAC,CAACP,IAAI;YAAA;YAAA;YAAA;UAAA,QAed,CAAC;QAAA;UAAA;UAAA;UAAA;QAAA,QACW;MAAA;QAAA;QAAA;QAAA;MAAA;IACN;MAAA;MAAA;MAAA;IAAA,QACP,EACLpB,SAAS,iBACR;MAAK,SAAS,EAAC,OAAO;MAAA,uBACpB,QAAC,MAAM;QACL,OAAO;QACP,KAAK,EAAC,QAAQ;QACd,OAAO,EAAE,MAAMK,oBAAoB,CAACP,OAAO,CAAE;QAC7C,IAAI,EAAC,IAAI;QAAA,uBACT,QAAC,eAAe;UAAC,IAAI,EAAE,CAAC,KAAK,EAAE,WAAW;QAAE;UAAA;UAAA;UAAA;QAAA;MAAG;QAAA;QAAA;QAAA;MAAA;IACxC;MAAA;MAAA;MAAA;IAAA,QAEZ;EAAA;IAAA;IAAA;IAAA;EAAA,QACG;AAEV;AAAC,GApHQD,WAAW;EAAA,QAcDF,OAAO,EAQPC,OAAO;AAAA;AAAA,KAtBjBC,WAAW;AAsHpB,OAAO,SAASoC,MAAM,GAAG;EAAA;EAAA;EACvB,MAAMC,QAAQ,GAAGlE,WAAW,EAAE;IAC5BmE,QAAQ,GAAGjE,WAAW,EAAE;IACxB;MAAEkE;IAAS,CAAC,GAAGnD,OAAO,EAAE;IACxB;MAAEoD;IAAG,CAAC,GAAGpE,SAAS,EAAkB;IACpCqE,MAAM,GAAGvE,WAAW,CAACmB,YAAY,CAAC;IAClCa,KAAK,GAAGhC,WAAW,CAACuB,WAAW,CAAC;IAChCW,OAAO,GAAGlC,WAAW,CAAC2B,aAAa,CAAC;IACpC,CAAC6C,YAAY,EAAEC,eAAe,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;IACjD,CAAC4E,UAAU,EAAEC,aAAa,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC;IAC1C,CAACqC,mBAAmB,EAAEyC,sBAAsB,CAAC,GAAG9E,QAAQ,CAA2B,CAAC,CAAC,CAAC;IACtF+E,KAAK,GAAG,CAAC7C,KAAK,CAAC8C,IAAI;IACnB7C,SAAS,GACN4C,KAAK,IAAIR,QAAQ,CAAC,eAAe,CAAC,IAAKA,QAAQ,CAAC,eAAe,CAAC;IACnEU,SAAS,GAAGV,QAAQ,CAAC,eAAe,CAAC;EAEvCxE,SAAS,CAAC,MAAM;IACd,MAAMmF,KAAK,GAAGT,MAAM,CAACU,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,GAAG,KAAKb,EAAE,CAAC;IAC9C,IAAIU,KAAK,IAAIA,KAAK,CAACG,GAAG,KAAKnD,KAAK,CAACmD,GAAG,EAAE;MACpChB,QAAQ,CAAC3C,QAAQ,CAACwD,KAAK,CAAC,CAAC;MACzBP,eAAe,CAAC,CAAC,CAACO,KAAK,CAACI,SAAS,CAAC;MAClCT,aAAa,CAAC,EAAE,CAAC;IACnB,CAAC,MAAM,IAAIL,EAAE,KAAK,KAAK,IAAItC,KAAK,CAAC8C,IAAI,EAAE;MACrCX,QAAQ,CAAC3C,QAAQ,CAACC,WAAW,EAAE,CAAC,CAAC;MACjCgD,eAAe,CAAC,KAAK,CAAC;MACtBE,aAAa,CAAC,EAAE,CAAC;IACnB;EACF,CAAC,EAAE,CAACR,QAAQ,EAAEG,EAAE,EAAEtC,KAAK,EAAEuC,MAAM,CAAC,CAAC;EAEjC1E,SAAS,CAAC,MAAM;IACd,OAAO,SAASwF,OAAO,GAAG;MACxBlB,QAAQ,CAAC3C,QAAQ,CAACC,WAAW,EAAE,CAAC,CAAC;IACnC,CAAC;EACH,CAAC,EAAE,CAAC0C,QAAQ,CAAC,CAAC;EAEd,MAAMmB,gBAAgB,GAAIhC,CAAsC,IAAK;IACnE,MAAMiC,IAAI,GAAGjC,CAAC,CAACkC,MAAM,CAACC,KAAK;MACzBpC,IAAI,GAAI,GAAEkC,IAAK,IAAGvD,KAAK,CAAC0D,IAAK,EAAC;MAC9BC,MAAM,GAAG;QAAE,GAAG3D,KAAK;QAAEuD,IAAI;QAAElC;MAAK,CAAC;IAEnCc,QAAQ,CAAC3C,QAAQ,CAACmE,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAMC,gBAAgB,GAAItC,CAAsC,IAAK;IACnE,MAAMoC,IAAI,GAAGpC,CAAC,CAACkC,MAAM,CAACC,KAAK;MACzBpC,IAAI,GAAI,GAAErB,KAAK,CAACuD,IAAK,IAAGG,IAAK,EAAC;MAC9BC,MAAM,GAAG;QAAE,GAAG3D,KAAK;QAAE0D,IAAI;QAAErC;MAAK,CAAC;IAEnCc,QAAQ,CAAC3C,QAAQ,CAACmE,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAME,wBAAwB,GAAIvC,CAAsC,IAAK;IAC3E,MAAMwC,YAAY,GAAGxC,CAAC,CAACkC,MAAM,CAACC,KAAK;MACjCE,MAAM,GAAG;QAAE,GAAG3D,KAAK;QAAE8D;MAAa,CAAC;IAErC3B,QAAQ,CAAC3C,QAAQ,CAACmE,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAMI,0BAA0B,GAC9BzC,CAAsC,IACnC;IACH,MAAM0C,cAAc,GAAG1C,CAAC,CAACkC,MAAM,CAACS,aAAa;MAC3CN,MAAM,GAAG;QAAE,GAAG3D,KAAK;QAAEgE;MAAe,CAAC;IAEvC7B,QAAQ,CAAC3C,QAAQ,CAACmE,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAMO,mCAAmC,GACvC5C,CAAsC,IACnC;IACH,MAAM6C,uBAAuB,GAAG7C,CAAC,CAACkC,MAAM,CAACS,aAAa;MACpDN,MAAM,GAAG;QAAE,GAAG3D,KAAK;QAAEmE;MAAwB,CAAC;IAEhDhC,QAAQ,CAAC3C,QAAQ,CAACmE,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAMS,iCAAiC,GACrC9C,CAAsC,IACnC;IACH,MAAM+C,qBAAqB,GAAG/C,CAAC,CAACkC,MAAM,CAACS,aAAa;MAClDN,MAAM,GAAG;QAAE,GAAG3D,KAAK;QAAEqE;MAAsB,CAAC;IAE9ClC,QAAQ,CAAC3C,QAAQ,CAACmE,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAMW,kCAAkC,GACtChD,CAAsC,IACnC;IACH,MAAMiD,sBAAsB,GAAGjD,CAAC,CAACkC,MAAM,CAACS,aAAa;MACnDN,MAAM,GAAG;QAAE,GAAG3D,KAAK;QAAEuE;MAAuB,CAAC;IAE/CpC,QAAQ,CAAC3C,QAAQ,CAACmE,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAMa,2CAA2C,GAC/ClD,CAAsC,IACnC;IACH,MAAMmD,+BAA+B,GAAGnD,CAAC,CAACkC,MAAM,CAACS,aAAa;MAC5DN,MAAM,GAAG;QAAE,GAAG3D,KAAK;QAAEyE;MAAgC,CAAC;IAExDtC,QAAQ,CAAC3C,QAAQ,CAACmE,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAMe,uBAAuB,GAAIpD,CAAsC,IAAK;IAC1E,MAAMqD,WAAW,GAAGrD,CAAC,CAACkC,MAAM,CAACS,aAAa;MACxCN,MAAM,GAAG;QAAE,GAAG3D,KAAK;QAAE2E;MAAY,CAAC;IAEpCxC,QAAQ,CAAC3C,QAAQ,CAACmE,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAMiB,wBAAwB,GAAItD,CAAsC,IAAK;IAC3E,MAAMuD,YAAY,GAAGvD,CAAC,CAACkC,MAAM,CAACsB,OAAO;MACnCnB,MAAM,GAAG;QAAE,GAAG3D,KAAK;QAAE6E;MAAa,CAAC;IAErC1C,QAAQ,CAAC3C,QAAQ,CAACmE,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAMoB,uBAAuB,GAAIzD,CAAsC,IAAK;IAC1E,MAAM0D,WAAW,GAAG1D,CAAC,CAACkC,MAAM,CAACsB,OAAO;MAClCnB,MAAM,GAAG;QAAE,GAAG3D,KAAK;QAAEgF;MAAY,CAAC;IAEpC,IAAI,CAACA,WAAW,EAAE;MAChBrB,MAAM,CAACsB,mBAAmB,GAAG,CAAC;MAC9B,OAAOtB,MAAM,CAACuB,WAAW;IAC3B,CAAC,MAAM;MACL,IAAI,CAACvB,MAAM,CAACuB,WAAW,EAAE;QACvBvB,MAAM,CAACuB,WAAW,GAAG,CAAC;MACxB;IACF;IAEA/C,QAAQ,CAAC3C,QAAQ,CAACmE,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAMwB,uBAAuB,GAAI7D,CAAsC,IAAK;IAC1E,MAAM4D,WAAW,GAAG5D,CAAC,CAACkC,MAAM,CAACS,aAAa,IAAI,CAAC;MAC7CN,MAAM,GAAG;QAAE,GAAG3D,KAAK;QAAEkF;MAAY,CAAC;IAEpC/C,QAAQ,CAAC3C,QAAQ,CAACmE,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAMyB,kCAAkC,GACtC9D,CAAsC,IACnC;IACH,MAAM2D,mBAAmB,GAAG3D,CAAC,CAACkC,MAAM,CAACS,aAAa;MAChDN,MAAM,GAAG;QAAE,GAAG3D,KAAK;QAAEiF;MAAoB,CAAC;IAE5C9C,QAAQ,CAAC3C,QAAQ,CAACmE,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAM0B,kBAAkB,GAAI/D,CAAsC,IAAK;IACrE,MAAMI,MAAM,GAAGJ,CAAC,CAACkC,MAAM,CAACC,KAAK;MAC3BE,MAAM,GAAG;QAAE,GAAG3D;MAAM,CAAC;IAEvB,IAAI0B,MAAM,EAAE;MACViC,MAAM,CAACjC,MAAM,GAAGA,MAAM;IACxB,CAAC,MAAM;MACL,OAAO1B,KAAK,CAAC0B,MAAM;IACrB;IAEAS,QAAQ,CAAC3C,QAAQ,CAACmE,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAM2B,sBAAsB,GAAG,MAAM;IACnC,MAAM3B,MAAM,GAAG;MAAE,GAAG3D;IAAM,CAAC;IAC3B,OAAO2D,MAAM,CAACjC,MAAM;IACpBS,QAAQ,CAAC3C,QAAQ,CAACmE,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAM4B,oBAAoB,GAAG,MAAM;IACjC,MAAM5B,MAAM,GAAG;MAAE,GAAG3D,KAAK;MAAE0B,MAAM,EAAE;IAAU,CAAC;IAC9CS,QAAQ,CAAC3C,QAAQ,CAACmE,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAM6B,wBAAwB,GAAIlE,CAAsC,IAAK;IAC3E,MAAMkB,YAAY,GAAGlB,CAAC,CAACkC,MAAM,CAACsB,OAAO;MACnCnB,MAAM,GAAG;QAAE,GAAG3D;MAAM,CAAC;IAEvB,IAAIwC,YAAY,EAAE;MAChBmB,MAAM,CAACP,SAAS,GAAG,EAAE;IACvB,CAAC,MAAM;MACL,OAAOO,MAAM,CAACP,SAAS;IACzB;IAEAjB,QAAQ,CAAC3C,QAAQ,CAACmE,MAAM,CAAC,CAAC;IAE1BlB,eAAe,CAACD,YAAY,CAAC;EAC/B,CAAC;EAED,MAAMiD,mCAAmC,GACvCnE,CAAsC,IACnC;IACH,MAAMoE,uBAAuB,GAAGpE,CAAC,CAACkC,MAAM,CAACS,aAAa;MACpDN,MAAM,GAAG;QAAE,GAAG3D,KAAK;QAAE0F;MAAwB,CAAC;IAEhDvD,QAAQ,CAAC3C,QAAQ,CAACmE,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAMgC,8BAA8B,GAClCrE,CAAsC,IACnC;IACH,MAAMsE,kBAAkB,GAAGtE,CAAC,CAACkC,MAAM,CAACS,aAAa;MAC/CN,MAAM,GAAG;QAAE,GAAG3D,KAAK;QAAE4F;MAAmB,CAAC;IAE3CzD,QAAQ,CAAC3C,QAAQ,CAACmE,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAMkC,+BAA+B,GACnCvE,CAAsC,IACnC;IACH,MAAMwE,mBAAmB,GAAGxE,CAAC,CAACkC,MAAM,CAACS,aAAa;MAChDN,MAAM,GAAG;QAAE,GAAG3D,KAAK;QAAE8F;MAAoB,CAAC;IAE5C3D,QAAQ,CAAC3C,QAAQ,CAACmE,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAMoC,sBAAsB,GAAIzE,CAAsC,IAAK;IACzE,MAAMoB,UAAU,GAAGpB,CAAC,CAACkC,MAAM,CAACC,KAAK;IACjCd,aAAa,CAACD,UAAU,CAAC;EAC3B,CAAC;EAED,MAAMsD,qBAAqB,GAAI1E,CAAsB,IAAK;IACxD,IAAIoB,UAAU,IAAIpB,CAAC,CAAC2E,GAAG,KAAK,OAAO,EAAE;MACnCC,wBAAwB,EAAE;IAC5B;EACF,CAAC;EAED,MAAMA,wBAAwB,GAAG,MAAM;IACrC,IAAIxD,UAAU,EAAE;MACd,MAAMiB,MAAM,GAAG;UAAE,GAAG3D;QAAM,CAAC;QACzBoD,SAAS,GAAG,CAACO,MAAM,CAACP,SAAS,IAAI,EAAE,EAAEzB,GAAG,CAAEwE,CAAC,KAAM;UAAE,GAAGA;QAAE,CAAC,CAAC,CAAC;MAE7D/C,SAAS,CAACgD,IAAI,CAAC;QAAE/E,IAAI,EAAEqB,UAAU;QAAE2D,iBAAiB,EAAE;MAAK,CAAC,CAAC;MAE7D1C,MAAM,CAACP,SAAS,GAAGA,SAAS;MAC5BjB,QAAQ,CAAC3C,QAAQ,CAACmE,MAAM,CAAC,CAAC;MAC1BhB,aAAa,CAAC,EAAE,CAAC;IACnB;EACF,CAAC;EAED,MAAM2D,uBAAuB,GAAG,CAC9BhF,CAAsC,EACtCvB,OAAgB,KACb;IACH,MAAM4D,MAAM,GAAG;QAAE,GAAG3D;MAAM,CAAC;MACzBoD,SAAS,GAAG,CAACO,MAAM,CAACP,SAAS,IAAI,EAAE,EAAEzB,GAAG,CAAEwE,CAAC,KAAM;QAAE,GAAGA;MAAE,CAAC,CAAC,CAAC;MAC3DI,KAAK,GAAGnD,SAAS,CAACoD,SAAS,CAAEL,CAAC,IAAKA,CAAC,CAAC9E,IAAI,KAAKtB,OAAO,CAACsB,IAAI,CAAC;IAE7D,IAAIkF,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,MAAMlF,IAAI,GAAGC,CAAC,CAACkC,MAAM,CAACC,KAAK;QACzBgD,OAAO,GAAG;UAAE,GAAGrD,SAAS,CAACmD,KAAK,CAAC;UAAElF;QAAK,CAAC;MACzC+B,SAAS,CAACsD,MAAM,CAACH,KAAK,EAAE,CAAC,EAAEE,OAAO,CAAC;IACrC;IAEA9C,MAAM,CAACP,SAAS,GAAGA,SAAS;IAE5BjB,QAAQ,CAAC3C,QAAQ,CAACmE,MAAM,CAAC,CAAC;EAC5B,CAAC;EAID,MAAMgD,wBAAwB,GAAI5G,OAAgB,IAAK;IACrD,MAAM4D,MAAM,GAAG;QAAE,GAAG3D;MAAM,CAAC;MACzBoD,SAAS,GAAG,CAACO,MAAM,CAACP,SAAS,IAAI,EAAE,EAAEzB,GAAG,CAAEwE,CAAC,KAAM;QAAE,GAAGA;MAAE,CAAC,CAAC,CAAC;MAC3DI,KAAK,GAAGnD,SAAS,CAACoD,SAAS,CAAEL,CAAC,IAAKA,CAAC,CAAC9E,IAAI,KAAKtB,OAAO,CAACsB,IAAI,CAAC;IAE7D,IAAIkF,KAAK,KAAK,CAAC,CAAC,EAAE;MAChBnD,SAAS,CAACsD,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;IAC5B;IAEA5C,MAAM,CAACP,SAAS,GAAGA,SAAS;IAE5BjB,QAAQ,CAAC3C,QAAQ,CAACmE,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAMiD,qBAAqB,GAAIC,WAAmB,IAAK;IACrDjE,sBAAsB,CAACkE,IAAI,KAAK;MAC9B,GAAGA,IAAI;MACP,CAACD,WAAW,GAAG,CAACC,IAAI,CAACD,WAAW;IAClC,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,yBAAyB,GAAG,CAAChH,OAAgB,EAAEiH,UAAkB,KAAK;IAC1E,MAAMtF,MAAM,GAAGsF,UAAU,GAAG9G,OAAO,CAAC+C,IAAI,CAAErB,CAAC,IAAKA,CAAC,CAACP,IAAI,KAAK2F,UAAU,CAAC,IAAI,IAAI,GAAG,IAAI;IACrF,MAAMrD,MAAM,GAAG;QAAE,GAAG3D;MAAM,CAAC;MACzBoD,SAAS,GAAG,CAACO,MAAM,CAACP,SAAS,IAAI,EAAE,EAAEzB,GAAG,CAAEwE,CAAC,KAAM;QAAE,GAAGA;MAAE,CAAC,CAAC,CAAC;MAC3DI,KAAK,GAAGnD,SAAS,CAACoD,SAAS,CAAEL,CAAC,IAAKA,CAAC,CAAC9E,IAAI,KAAKtB,OAAO,CAACsB,IAAI,CAAC;IAE7D,IAAIkF,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,MAAME,OAAO,GAAG;QAAE,GAAGrD,SAAS,CAACmD,KAAK,CAAC;QAAE7E;MAAO,CAAC;MAC/C0B,SAAS,CAACsD,MAAM,CAACH,KAAK,EAAE,CAAC,EAAEE,OAAO,CAAC;IACrC;IAEA9C,MAAM,CAACP,SAAS,GAAGA,SAAS;IAC5BjB,QAAQ,CAAC3C,QAAQ,CAACmE,MAAM,CAAC,CAAC;;IAE1B;IACAf,sBAAsB,CAACkE,IAAI,KAAK;MAC9B,GAAGA,IAAI;MACP,CAAC/G,OAAO,CAACsB,IAAI,GAAG;IAClB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAM4F,iBAAiB,GAAG,CAACC,eAAwB,EAAEC,aAAsB,KAAK;IAC9EhF,QAAQ,CAAC/C,kBAAkB,CAAC;MAAEgI,OAAO,EAAEpH,KAAK,CAACmD,GAAG;MAAE+D,eAAe;MAAEC;IAAc,CAAC,CAAC,CAAC;EACtF,CAAC;EAED,MAAME,eAAe,GAAG,YAAY;IAClC,MAAMC,MAAW,GAAG,MAAMnF,QAAQ,CAAC7C,SAAS,EAAE,CAAC;IAE/C,IAAI,CAACgI,MAAM,CAACC,KAAK,EAAE;MACjBnF,QAAQ,CAACnD,MAAM,CAACsD,MAAM,CAACiF,IAAI,CAAC;IAC9B;EACF,CAAC;EAED,MAAMC,iBAAiB,GAAG,YAAY;IACpC,MAAMH,MAAW,GAAG,MAAMnF,QAAQ,CAAC9C,WAAW,EAAE,CAAC;IAEjD,IAAI,CAACiI,MAAM,CAACC,KAAK,EAAE;MACjBnF,QAAQ,CAACnD,MAAM,CAACsD,MAAM,CAACiF,IAAI,CAAC;IAC9B;EACF,CAAC;EAED,oBACE;IAAK,SAAS,EAAC,wBAAwB;IAAA,wBACrC;MAAK,SAAS,EAAC,iDAAiD;MAAA,wBAC9D;QAAK,SAAS,EAAC,eAAe;QAAA,uBAC5B,QAAC,IAAI;UAAC,EAAE,EAAEvI,MAAM,CAACsD,MAAM,CAACiF,IAAK;UAAA,wBAC3B,QAAC,eAAe;YAAC,IAAI,EAAE,CAAC,KAAK,EAAE,cAAc;UAAE;YAAA;YAAA;YAAA;UAAA,QAAG;QAAA;UAAA;UAAA;UAAA;QAAA;MAE7C;QAAA;QAAA;QAAA;MAAA,QACH,eACN;QAAI,SAAS,EAAC,KAAK;QAAA,UAAE3E,KAAK,GAAG,WAAW,GAAG7C,KAAK,CAACqB;MAAI;QAAA;QAAA;QAAA;MAAA,QAAM;IAAA;MAAA;MAAA;MAAA;IAAA,QACvD,eACN;MAAK,SAAS,EAAC,KAAK;MAAA,wBAClB;QAAK,SAAS,EAAC,iBAAiB;QAAA,wBAC9B;UAAO,OAAO,EAAC,YAAY;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QAAa,eACxC,QAAC,KAAK;UACJ,EAAE,EAAC,YAAY;UACf,KAAK,EAAErB,KAAK,CAACuD,IAAK;UAClB,QAAQ,EAAED,gBAAiB;UAC3B,QAAQ,EAAE,CAACrD;QAAU;UAAA;UAAA;UAAA;QAAA,QACrB;MAAA;QAAA;QAAA;QAAA;MAAA,QACE,eACN;QAAK,SAAS,EAAC,iBAAiB;QAAA,wBAC9B;UAAO,OAAO,EAAC,YAAY;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QAAa,eACxC,QAAC,KAAK;UACJ,EAAE,EAAC,YAAY;UACf,KAAK,EAAED,KAAK,CAAC0D,IAAK;UAClB,QAAQ,EAAEE,gBAAiB;UAC3B,QAAQ,EAAE,CAAC3D;QAAU;UAAA;UAAA;UAAA;QAAA,QACrB;MAAA;QAAA;QAAA;QAAA;MAAA,QACE,eACN;QAAK,SAAS,EAAC,iBAAiB;QAAA,wBAC9B;UAAO,OAAO,EAAC,oBAAoB;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QAAqB,eACxD,QAAC,KAAK;UACJ,EAAE,EAAC,oBAAoB;UACvB,KAAK,EAAED,KAAK,CAAC8D,YAAa;UAC1B,QAAQ,EAAED,wBAAyB;UACnC,QAAQ,EAAE,CAAC5D;QAAU;UAAA;UAAA;UAAA;QAAA,QACrB;MAAA;QAAA;QAAA;QAAA;MAAA,QACE;IAAA;MAAA;MAAA;MAAA;IAAA,QACF,eACN;MAAK,SAAS,EAAC,KAAK;MAAA,wBAClB;QAAK,SAAS,EAAC,iBAAiB;QAAA,wBAC9B;UAAO,OAAO,EAAC,wBAAwB;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QAAyB,eAChE,QAAC,KAAK;UACJ,EAAE,EAAC,wBAAwB;UAC3B,IAAI,EAAC,QAAQ;UACb,KAAK,EAAED,KAAK,CAACgE,cAAe;UAC5B,QAAQ,EAAED,0BAA2B;UACrC,OAAO,EAAErE,WAAY;UACrB,QAAQ,EAAE,CAACO;QAAU;UAAA;UAAA;UAAA;QAAA,QACrB;MAAA;QAAA;QAAA;QAAA;MAAA,QACE,eACN;QAAK,SAAS,EAAC,iBAAiB;QAAA,wBAC9B;UAAO,OAAO,EAAC,qBAAqB;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QAAsB,eAC1D,QAAC,KAAK;UACJ,EAAE,EAAC,qBAAqB;UACxB,IAAI,EAAC,QAAQ;UACb,KAAK,EAAED,KAAK,CAAC2E,WAAY;UACzB,QAAQ,EAAED,uBAAwB;UAClC,OAAO,EAAEhF,WAAY;UACrB,QAAQ,EAAE,CAACO;QAAU;UAAA;UAAA;UAAA;QAAA,QACrB;MAAA;QAAA;QAAA;QAAA;MAAA,QACE,eACN;QAAK,SAAS,EAAC,iBAAiB;QAAA,wBAC9B;UAAO,OAAO,EAAC,kCAAkC;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QAEzC,eACR,QAAC,KAAK;UACJ,EAAE,EAAC,kCAAkC;UACrC,IAAI,EAAC,QAAQ;UACb,KAAK,EAAED,KAAK,CAACmE,uBAAwB;UACrC,QAAQ,EAAED,mCAAoC;UAC9C,OAAO,EAAExE,WAAY;UACrB,QAAQ,EAAE,CAACO;QAAU;UAAA;UAAA;UAAA;QAAA,QACrB;MAAA;QAAA;QAAA;QAAA;MAAA,QACE;IAAA;MAAA;MAAA;MAAA;IAAA,QACF,eACN;MAAK,SAAS,EAAC,KAAK;MAAA,wBAClB;QAAK,SAAS,EAAC,iBAAiB;QAAA,wBAC9B;UAAO,OAAO,EAAC,gCAAgC;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QAEvC,eACR,QAAC,KAAK;UACJ,EAAE,EAAC,gCAAgC;UACnC,IAAI,EAAC,QAAQ;UACb,KAAK,EAAED,KAAK,CAACqE,qBAAsB;UACnC,QAAQ,EAAED,iCAAkC;UAC5C,OAAO,EAAE1E,WAAY;UACrB,QAAQ,EAAE,CAACO;QAAU;UAAA;UAAA;UAAA;QAAA,QACrB;MAAA;QAAA;QAAA;QAAA;MAAA,QACE,eACN;QAAK,SAAS,EAAC,iBAAiB;QAAA,wBAC9B;UAAO,OAAO,EAAC,2CAA2C;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QAElD,eACR,QAAC,KAAK;UACJ,EAAE,EAAC,2CAA2C;UAC9C,IAAI,EAAC,QAAQ;UACb,KAAK,EAAED,KAAK,CAACyE,+BAAgC;UAC7C,QAAQ,EAAED,2CAA4C;UACtD,OAAO,EAAE9E,WAAY;UACrB,QAAQ,EAAE,CAACO;QAAU;UAAA;UAAA;UAAA;QAAA,QACrB;MAAA;QAAA;QAAA;QAAA;MAAA,QACE,eACN;QAAK,SAAS,EAAC,iBAAiB;QAAA,wBAC9B;UAAO,OAAO,EAAC,iCAAiC;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QAExC,eACR,QAAC,KAAK;UACJ,EAAE,EAAC,iCAAiC;UACpC,IAAI,EAAC,QAAQ;UACb,KAAK,EAAED,KAAK,CAACuE,sBAAuB;UACpC,QAAQ,EAAED,kCAAmC;UAC7C,OAAO,EAAE5E,WAAY;UACrB,QAAQ,EAAE,CAACO;QAAU;UAAA;UAAA;UAAA;QAAA,QACrB;MAAA;QAAA;QAAA;QAAA;MAAA,QACE;IAAA;MAAA;MAAA;MAAA;IAAA,QACF,eACN;MAAK,SAAS,EAAC,KAAK;MAAA,wBAClB;QAAK,SAAS,EAAC,iBAAiB;QAAA,wBAC9B;UAAO,OAAO,EAAC,kCAAkC;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QAEzC,eACR,QAAC,KAAK;UACJ,EAAE,EAAC,kCAAkC;UACrC,IAAI,EAAC,QAAQ;UACb,KAAK,EAAED,KAAK,CAAC0F,uBAAwB;UACrC,QAAQ,EAAED,mCAAoC;UAC9C,OAAO,EAAE/F,WAAY;UACrB,QAAQ,EAAE,CAACO;QAAU;UAAA;UAAA;UAAA;QAAA,QACrB;MAAA;QAAA;QAAA;QAAA;MAAA,QACE,eACN;QAAK,SAAS,EAAC,iBAAiB;QAAA,wBAC9B;UAAO,OAAO,EAAC,6BAA6B;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QAEpC,eACR,QAAC,KAAK;UACJ,EAAE,EAAC,6BAA6B;UAChC,IAAI,EAAC,QAAQ;UACb,KAAK,EAAED,KAAK,CAAC4F,kBAAmB;UAChC,QAAQ,EAAED,8BAA+B;UACzC,OAAO,EAAEjG,WAAY;UACrB,QAAQ,EAAE,CAACO;QAAU;UAAA;UAAA;UAAA;QAAA,QACrB;MAAA;QAAA;QAAA;QAAA;MAAA,QACE,eACN;QAAK,SAAS,EAAC,iBAAiB;QAAA,wBAC9B;UAAO,OAAO,EAAC,8BAA8B;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QAErC,eACR,QAAC,KAAK;UACJ,EAAE,EAAC,8BAA8B;UACjC,IAAI,EAAC,QAAQ;UACb,KAAK,EAAED,KAAK,CAAC8F,mBAAoB;UACjC,QAAQ,EAAED,+BAAgC;UAC1C,OAAO,EAAEnG,WAAY;UACrB,QAAQ,EAAE,CAACO;QAAU;UAAA;UAAA;UAAA;QAAA,QACrB;MAAA;QAAA;QAAA;QAAA;MAAA,QACE,EACL,CAAC,CAACD,KAAK,CAACgF,WAAW,iBAClB;QAAK,SAAS,EAAC,iBAAiB;QAAA,wBAC9B;UAAO,OAAO,EAAC,8BAA8B;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QAErC,eACR,QAAC,KAAK;UACJ,EAAE,EAAC,8BAA8B;UACjC,IAAI,EAAC,QAAQ;UACb,KAAK,EAAEhF,KAAK,CAACiF,mBAAoB;UACjC,QAAQ,EAAEG,kCAAmC;UAC7C,OAAO,EAAE1F,WAAY;UACrB,QAAQ,EAAE,CAACO;QAAU;UAAA;UAAA;UAAA;QAAA,QACrB;MAAA;QAAA;QAAA;QAAA;MAAA,QAEL;IAAA;MAAA;MAAA;MAAA;IAAA,QACG,eACN;MAAK,SAAS,EAAC,UAAU;MAAA,wBACvB;QAAK,SAAS,EAAC,iBAAiB;QAAA,uBAC9B;UAAK,SAAS,EAAC,YAAY;UAAA,wBACzB,QAAC,KAAK;YACJ,EAAE,EAAC,sBAAsB;YACzB,IAAI,EAAC,UAAU;YACf,OAAO,EAAED,KAAK,CAAC6E,YAAa;YAC5B,QAAQ,EAAED,wBAAyB;YACnC,QAAQ,EAAE,CAAC3E;UAAU;YAAA;YAAA;YAAA;UAAA,QACrB,eACF;YAAO,OAAO,EAAC,sBAAsB;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAuB;QAAA;UAAA;UAAA;UAAA;QAAA;MACxD;QAAA;QAAA;QAAA;MAAA,QACF,eACN;QAAK,SAAS,EAAC,iBAAiB;QAAA,wBAC9B;UAAK,SAAS,EAAC,YAAY;UAAA,wBACzB,QAAC,KAAK;YACJ,EAAE,EAAC,sBAAsB;YACzB,IAAI,EAAC,UAAU;YACf,OAAO,EAAED,KAAK,CAACgF,WAAY;YAC3B,QAAQ,EAAED,uBAAwB;YAClC,QAAQ,EAAE,CAAC9E;UAAU;YAAA;YAAA;YAAA;UAAA,QACrB,eACF;YAAO,OAAO,EAAC,sBAAsB;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAuB;QAAA;UAAA;UAAA;UAAA;QAAA,QACxD,EACL,CAAC,CAACD,KAAK,CAACgF,WAAW,iBAClB,QAAC,SAAS;UAAA,wBACR;YAAO,OAAO,EAAC,8BAA8B;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAErC,eACR,QAAC,KAAK;YACJ,EAAE,EAAC,8BAA8B;YACjC,IAAI,EAAC,QAAQ;YACb,KAAK,EAAEhF,KAAK,CAACkF,WAAW,IAAI,EAAG;YAC/B,QAAQ,EAAEC,uBAAwB;YAClC,OAAO,EAAEzF,WAAY;YACrB,QAAQ,EAAE,CAACO;UAAU;YAAA;YAAA;YAAA;UAAA,QACrB;QAAA;UAAA;UAAA;UAAA;QAAA,QAEL;MAAA;QAAA;QAAA;QAAA;MAAA,QACG,eACN;QAAK,SAAS,EAAC,iBAAiB;QAAA,wBAC9B;UAAO,OAAO,EAAC,cAAc;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QAAe,EAC3C,CAAC,CAACD,KAAK,CAAC0B,MAAM,iBACb,QAAC,UAAU;UAAA,wBACT,QAAC,KAAK;YACJ,EAAE,EAAC,cAAc;YACjB,IAAI,EAAC,OAAO;YACZ,KAAK,EAAE1B,KAAK,CAAC0B,MAAM,IAAI,EAAG;YAC1B,QAAQ,EAAE2D,kBAAmB;YAC7B,QAAQ,EAAE,CAACpF;UAAU;YAAA;YAAA;YAAA;UAAA,QACrB,EACDA,SAAS,iBACR,QAAC,MAAM;YACL,IAAI,EAAC,IAAI;YACT,KAAK,EAAC,QAAQ;YACd,OAAO;YACP,OAAO,EAAEqF,sBAAuB;YAAA,uBAChC,QAAC,eAAe;cAAC,IAAI,EAAE,CAAC,KAAK,EAAE,OAAO;YAAE;cAAA;cAAA;cAAA;YAAA;UAAG;YAAA;YAAA;YAAA;UAAA,QAE9C;QAAA;UAAA;UAAA;UAAA;QAAA,QAEJ,EACA,CAACtF,KAAK,CAAC0B,MAAM,iBACZ,QAAC,UAAU;UAAA,wBACT,QAAC,cAAc;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAA2B,EACzCzB,SAAS,iBACR;YAAA,wBACE,QAAC,MAAM;cACL,EAAE,EAAC,YAAY;cACf,IAAI,EAAC,IAAI;cACT,KAAK,EAAC,SAAS;cACf,OAAO;cACP,OAAO,EAAEsF,oBAAqB;cAAA,uBAC9B,QAAC,eAAe;gBAAC,IAAI,EAAE,CAAC,KAAK,EAAE,SAAS;cAAE;gBAAA;gBAAA;gBAAA;cAAA;YAAG;cAAA;cAAA;cAAA;YAAA,QACtC,eACT,QAAC,mBAAmB;cAAC,MAAM,EAAC,YAAY;cAAA;YAAA;cAAA;cAAA;cAAA;YAAA,QAElB;UAAA,gBAEzB;QAAA;UAAA;UAAA;UAAA;QAAA,QAEJ;MAAA;QAAA;QAAA;QAAA;MAAA,QACG;IAAA;MAAA;MAAA;MAAA;IAAA,QACF,eACN;MAAK,SAAS,EAAC,KAAK;MAAA,wBAClB;QAAK,SAAS,EAAC,iBAAiB;QAAA,uBAC9B;UAAK,SAAS,EAAC,YAAY;UAAA,wBACzB,QAAC,KAAK;YACJ,EAAE,EAAC,qBAAqB;YACxB,IAAI,EAAC,UAAU;YACf,OAAO,EAAE/C,YAAa;YACtB,QAAQ,EAAEgD,wBAAyB;YACnC,QAAQ,EAAE,CAACvF;UAAU;YAAA;YAAA;YAAA;UAAA,QACrB,eACF;YAAO,OAAO,EAAC,qBAAqB;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAsB;QAAA;UAAA;UAAA;UAAA;QAAA;MACtD;QAAA;QAAA;QAAA;MAAA,QACF,EACLuC,YAAY,iBACX;QAAK,SAAS,EAAC,OAAO;QAAA,WACnBvC,SAAS,iBACR;UAAK,SAAS,EAAC,KAAK;UAAA,wBAClB;YAAK,SAAS,EAAC,OAAO;YAAA,uBACpB,QAAC,SAAS;cAAC,QAAQ;cAAA,wBACjB,QAAC,KAAK;gBACJ,EAAE,EAAC,kBAAkB;gBACrB,KAAK,EAAEyC,UAAW;gBAClB,QAAQ,EAAEqD,sBAAuB;gBACjC,OAAO,EAAEC,qBAAsB;gBAC/B,WAAW,EAAC;cAAa;gBAAA;gBAAA;gBAAA;cAAA,QACzB,eACF,QAAC,KAAK;gBAAC,OAAO,EAAC,kBAAkB;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA,QAAoB;YAAA;cAAA;cAAA;cAAA;YAAA;UAC3C;YAAA;YAAA;YAAA;UAAA,QACR,eACN;YAAK,SAAS,EAAC,OAAO;YAAA,uBACpB,QAAC,MAAM;cACL,OAAO;cACP,KAAK,EAAC,SAAS;cACf,OAAO,EAAEE,wBAAyB;cAClC,QAAQ,EAAE,CAACxD,UAAW;cACtB,IAAI,EAAC,IAAI;cAAA,uBACT,QAAC,eAAe;gBAAC,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM;cAAE;gBAAA;gBAAA;gBAAA;cAAA;YAAG;cAAA;cAAA;cAAA;YAAA;UACnC;YAAA;YAAA;YAAA;UAAA,QACL;QAAA;UAAA;UAAA;UAAA;QAAA,QAET,sBACA1C,KAAK,CAACoD,SAAS,qDAAf,iBAAiBzB,GAAG,CAAE5B,OAAO,iBAC5B;UAAK,SAAS,EAAC,KAAK;UAAA,wBAClB;YAAK,SAAS,EAAC,OAAO;YAAA,uBACpB,QAAC,KAAK;cACJ,KAAK,EAAEA,OAAO,CAACsB,IAAK;cACpB,QAAQ,EAAGC,CAAC,IAAKgF,uBAAuB,CAAChF,CAAC,EAAEvB,OAAO,CAAE;cACrD,QAAQ,EAAE,CAACE;YAAU;cAAA;cAAA;cAAA;YAAA;UACrB;YAAA;YAAA;YAAA;UAAA,QACE,eACN;YAAK,SAAS,EAAC,OAAO;YAAA,uBACpB,QAAC,QAAQ;cACP,MAAM,EAAEE,mBAAmB,CAACJ,OAAO,CAACsB,IAAI,CAAC,IAAI,KAAM;cACnD,MAAM,EAAE,MAAMuF,qBAAqB,CAAC7G,OAAO,CAACsB,IAAI,CAAE;cAClD,QAAQ,EAAE,CAACpB,SAAU;cAAA,wBACrB,QAAC,cAAc;gBACb,KAAK;gBACL,SAAS,EAAC,oEAAoE;gBAC9E,KAAK,EAAE;kBACLsB,eAAe,EAAE,OAAO;kBACxBC,WAAW,EAAE,SAAS;kBACtBC,KAAK,EAAE;gBACT,CAAE;gBAAA,uBACF;kBAAK,SAAS,EAAC,2BAA2B;kBAAA,UACvC1B,OAAO,CAAC2B,MAAM,gBACb;oBAAA,wBACE;sBACE,SAAS,EAAC;oBAAwB;sBAAA;sBAAA;sBAAA;oBAAA,QAClC,EACD3B,OAAO,CAAC2B,MAAM,CAACL,IAAI;kBAAA,gBACnB,GAEH;gBACD;kBAAA;kBAAA;kBAAA;gBAAA;cACG;gBAAA;gBAAA;gBAAA;cAAA,QACS,eACjB,QAAC,YAAY;gBAAC,SAAS,EAAC,OAAO;gBAAA,wBAC7B,QAAC,YAAY;kBAAC,OAAO,EAAE,MAAM0F,yBAAyB,CAAChH,OAAO,EAAE,EAAE,CAAE;kBAAA;gBAAA;kBAAA;kBAAA;kBAAA;gBAAA,QAErD,EACdG,OAAO,CAACyB,GAAG,CAAEC,CAAC,iBACb,QAAC,YAAY;kBAEX,OAAO,EAAE,MAAMmF,yBAAyB,CAAChH,OAAO,EAAE6B,CAAC,CAACP,IAAI,CAAE;kBAC1D,SAAS,EAAC,2BAA2B;kBAAA,wBACrC;oBACE,KAAK,EAAE;sBACLQ,KAAK,EAAE,MAAM;sBACbT,MAAM,EAAE,MAAM;sBACdG,eAAe,EAAEK,CAAC,CAACE,GAAG;sBACtBC,MAAM,EAAE,gBAAgB;sBACxBC,WAAW,EAAE,KAAK;sBAClBC,YAAY,EAAE;oBAChB;kBAAE;oBAAA;oBAAA;oBAAA;kBAAA,QACF,EACDL,CAAC,CAACP,IAAI;gBAAA,GAbFO,CAAC,CAACP,IAAI;kBAAA;kBAAA;kBAAA;gBAAA,QAed,CAAC;cAAA;gBAAA;gBAAA;gBAAA;cAAA,QACW;YAAA;cAAA;cAAA;cAAA;YAAA;UACN;YAAA;YAAA;YAAA;UAAA,QACP,EACLpB,SAAS,iBACR;YAAK,SAAS,EAAC,OAAO;YAAA,uBACpB,QAAC,MAAM;cACL,OAAO;cACP,KAAK,EAAC,QAAQ;cACd,OAAO,EAAE,MAAM0G,wBAAwB,CAAC5G,OAAO,CAAE;cACjD,IAAI,EAAC,IAAI;cAAA,uBACT,QAAC,eAAe;gBAAC,IAAI,EAAE,CAAC,KAAK,EAAE,WAAW;cAAE;gBAAA;gBAAA;gBAAA;cAAA;YAAG;cAAA;cAAA;cAAA;YAAA;UACxC;YAAA;YAAA;YAAA;UAAA,QAEZ;QAAA;UAAA;UAAA;UAAA;QAAA,QAEJ,CAAC;MAAA;QAAA;QAAA;QAAA;MAAA,QAEL;IAAA;MAAA;MAAA;MAAA;IAAA,QACG,eACN;MAAK,SAAS,EAAC,4CAA4C;MAAA,WACxD,CAAC8C,KAAK,IAAIE,SAAS,iBAClB;QAAK,SAAS,EAAC,UAAU;QAAA,uBACvB,QAAC,MAAM;UACL,OAAO,EAAE0E,iBAAkB;UAC3B,OAAO;UACP,KAAK,EAAC,QAAQ;UACd,IAAI,EAAC,IAAI;UACT,SAAS,EAAC,SAAS;UAAA,wBACnB,QAAC,eAAe;YAAC,IAAI,EAAE,CAAC,KAAK,EAAE,WAAW;UAAE;YAAA;YAAA;YAAA;UAAA,QAAG;QAAA;UAAA;UAAA;UAAA;QAAA;MAExC;QAAA;QAAA;QAAA;MAAA,QAEZ,eACD;QAAK,SAAS,EAAC,cAAc;QAAA,wBAC3B,QAAC,MAAM;UAAC,GAAG,EAAErJ,IAAK;UAAC,EAAE,EAAEa,MAAM,CAACsD,MAAM,CAACiF,IAAK;UAAC,OAAO;UAAC,IAAI,EAAC,IAAI;UAAA,UACzDvH,SAAS,GAAG,QAAQ,GAAG;QAAO;UAAA;UAAA;UAAA;QAAA,QACxB,EACRA,SAAS,iBACR;UAAA,gCAEE,QAAC,MAAM;YAAC,OAAO,EAAEoH,eAAgB;YAAC,KAAK,EAAC,SAAS;YAAC,IAAI,EAAC,IAAI;YAAA,wBACzD,QAAC,eAAe;cAAC,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM;YAAE;cAAA;cAAA;cAAA;YAAA,QAAG;UAAA;YAAA;YAAA;YAAA;UAAA,QAEnC;QAAA,gBAEZ;MAAA;QAAA;QAAA;QAAA;MAAA,QACG;IAAA;MAAA;MAAA;MAAA;IAAA,QACF;EAAA;IAAA;IAAA;IAAA;EAAA,QACF;AAEV;AAAC,IAxtBenF,MAAM;EAAA,QACHjE,WAAW,EACfE,WAAW,EACPe,OAAO,EACbhB,SAAS,EACTF,WAAW,EACZA,WAAW,EACTA,WAAW;AAAA;AAAA,MAPTkE,MAAM;AA0tBtB,eAAeA,MAAM;AAAC;AAAA;AAAA"}, "metadata": {}, "sourceType": "module"}