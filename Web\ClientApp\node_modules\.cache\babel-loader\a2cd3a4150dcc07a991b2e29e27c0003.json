{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\GreenRP\\\\Web\\\\ClientApp\\\\src\\\\features\\\\plants\\\\List-Item.tsx\",\n  _s = $RefreshSig$();\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\nimport { routes } from \"app/routes\";\nimport { Link } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport function PlantListItem(_ref) {\n  _s();\n  let {\n    plant\n  } = _ref;\n  const ref = useRef(null),\n    dispatch = useDispatch(),\n    [, drag] = useDrag(() => ({\n      type: 'plant',\n      item: plant,\n      collect: monitor => ({\n        isDragging: monitor.isDragging()\n      })\n    })),\n    [, drop] = useDrop(() => ({\n      accept: 'plant',\n      drop: droppedItem => {\n        dispatch(moveItemAndSave({\n          existingItem: plant,\n          movingItem: droppedItem\n        }));\n      },\n      collect: monitor => ({\n        isOver: monitor.isOver()\n      })\n    }));\n  drag(drop(ref));\n  return /*#__PURE__*/_jsxDEV(\"tr\", {\n    ref: ref,\n    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"cursor-move\"\n        // @ts-ignore\n        ,\n        ref: drag,\n        children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n          icon: ['fat', 'grip-vertical']\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n      children: /*#__PURE__*/_jsxDEV(Link, {\n        to: routes.plants.routes.detail.to(plant._id),\n        children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n          icon: ['fat', 'edit']\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n      children: plant.abbreviation\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n      children: plant.name\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n      className: \"text-center\",\n      children: plant.cuttingsPerPot\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n      className: \"text-center\",\n      children: plant.potsPerCase\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n      className: \"text-center\",\n      children: plant.hasLightsOut && /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n        icon: ['fat', 'check-square']\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n      className: \"text-center\",\n      children: plant.hasPinching && /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n        icon: ['fat', 'check-square']\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this)]\n  }, plant._id, true, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 5\n  }, this);\n}\n_s(PlantListItem, \"k/UrYxCi2h11lIcWBX4E/Vcwe6Y=\", true);\n_c = PlantListItem;\nvar _c;\n$RefreshReg$(_c, \"PlantListItem\");", "map": {"version": 3, "names": ["FontAwesomeIcon", "routes", "Link", "PlantListItem", "plant", "ref", "useRef", "dispatch", "useDispatch", "drag", "useDrag", "type", "item", "collect", "monitor", "isDragging", "drop", "useDrop", "accept", "droppedItem", "moveItemAndSave", "existingItem", "movingItem", "isOver", "plants", "detail", "to", "_id", "abbreviation", "name", "cuttingsPerPot", "potsPerCase", "hasLightsOut", "hasPinching"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/plants/List-Item.tsx"], "sourcesContent": ["import { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { Plant } from \"api/models/plants\";\r\nimport { routes } from \"app/routes\";\r\nimport { Link } from \"react-router-dom\";\r\n\r\nexport type PlantListItemProps = {\r\n  plant: Plant;\r\n}\r\n\r\nexport function PlantListItem({plant}: PlantListItemProps) {\r\n  const ref = useRef<HTMLTableRowElement>(null),\r\n  dispatch = useDispatch(),\r\n  [, drag] = useDrag(() => ({\r\n    type: 'plant',\r\n    item: plant,\r\n    collect: (monitor) => ({\r\n      isDragging: monitor.isDragging(),\r\n    }),\r\n  })),\r\n  [, drop] = useDrop(() => ({\r\n    accept: 'plant',\r\n    drop: (droppedItem: Plant) => {\r\n      dispatch(moveItemAndSave({ existingItem: plant, movingItem: droppedItem }));\r\n    },\r\n    collect: (monitor) => ({\r\n      isOver: monitor.isOver(),\r\n    }),\r\n  }));\r\n\r\n  drag(drop(ref));\r\n\r\n  return (\r\n    <tr key={plant._id} ref={ref}>\r\n      <td>\r\n        <div \r\n          className=\"cursor-move\"\r\n          // @ts-ignore\r\n          ref={drag}>\r\n          <FontAwesomeIcon icon={['fat', 'grip-vertical']} />\r\n        </div>\r\n      </td>\r\n      <td>\r\n        <Link to={routes.plants.routes.detail.to(plant._id)}>\r\n          <FontAwesomeIcon icon={['fat', 'edit']} />\r\n        </Link>\r\n      </td>\r\n      <td>{plant.abbreviation}</td>\r\n      <td>{plant.name}</td>\r\n      <td className=\"text-center\">{plant.cuttingsPerPot}</td>\r\n      <td className=\"text-center\">{plant.potsPerCase}</td>\r\n      <td className=\"text-center\">\r\n        {plant.hasLightsOut &&\r\n          <FontAwesomeIcon icon={['fat', 'check-square']} />\r\n        }\r\n      </td>\r\n      <td className=\"text-center\">\r\n        {plant.hasPinching &&\r\n          <FontAwesomeIcon icon={['fat', 'check-square']} />\r\n        }\r\n      </td>\r\n    </tr>\r\n  )\r\n}"], "mappings": ";;AAAA,SAASA,eAAe,QAAQ,gCAAgC;AAEhE,SAASC,MAAM,QAAQ,YAAY;AACnC,SAASC,IAAI,QAAQ,kBAAkB;AAAC;AAMxC,OAAO,SAASC,aAAa,OAA8B;EAAA;EAAA,IAA7B;IAACC;EAAyB,CAAC;EACvD,MAAMC,GAAG,GAAGC,MAAM,CAAsB,IAAI,CAAC;IAC7CC,QAAQ,GAAGC,WAAW,EAAE;IACxB,GAAGC,IAAI,CAAC,GAAGC,OAAO,CAAC,OAAO;MACxBC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAER,KAAK;MACXS,OAAO,EAAGC,OAAO,KAAM;QACrBC,UAAU,EAAED,OAAO,CAACC,UAAU;MAChC,CAAC;IACH,CAAC,CAAC,CAAC;IACH,GAAGC,IAAI,CAAC,GAAGC,OAAO,CAAC,OAAO;MACxBC,MAAM,EAAE,OAAO;MACfF,IAAI,EAAGG,WAAkB,IAAK;QAC5BZ,QAAQ,CAACa,eAAe,CAAC;UAAEC,YAAY,EAAEjB,KAAK;UAAEkB,UAAU,EAAEH;QAAY,CAAC,CAAC,CAAC;MAC7E,CAAC;MACDN,OAAO,EAAGC,OAAO,KAAM;QACrBS,MAAM,EAAET,OAAO,CAACS,MAAM;MACxB,CAAC;IACH,CAAC,CAAC,CAAC;EAEHd,IAAI,CAACO,IAAI,CAACX,GAAG,CAAC,CAAC;EAEf,oBACE;IAAoB,GAAG,EAAEA,GAAI;IAAA,wBAC3B;MAAA,uBACE;QACE,SAAS,EAAC;QACV;QAAA;QACA,GAAG,EAAEI,IAAK;QAAA,uBACV,QAAC,eAAe;UAAC,IAAI,EAAE,CAAC,KAAK,EAAE,eAAe;QAAE;UAAA;UAAA;UAAA;QAAA;MAAG;QAAA;QAAA;QAAA;MAAA;IAC/C;MAAA;MAAA;MAAA;IAAA,QACH,eACL;MAAA,uBACE,QAAC,IAAI;QAAC,EAAE,EAAER,MAAM,CAACuB,MAAM,CAACvB,MAAM,CAACwB,MAAM,CAACC,EAAE,CAACtB,KAAK,CAACuB,GAAG,CAAE;QAAA,uBAClD,QAAC,eAAe;UAAC,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM;QAAE;UAAA;UAAA;UAAA;QAAA;MAAG;QAAA;QAAA;QAAA;MAAA;IACrC;MAAA;MAAA;MAAA;IAAA,QACJ,eACL;MAAA,UAAKvB,KAAK,CAACwB;IAAY;MAAA;MAAA;MAAA;IAAA,QAAM,eAC7B;MAAA,UAAKxB,KAAK,CAACyB;IAAI;MAAA;MAAA;MAAA;IAAA,QAAM,eACrB;MAAI,SAAS,EAAC,aAAa;MAAA,UAAEzB,KAAK,CAAC0B;IAAc;MAAA;MAAA;MAAA;IAAA,QAAM,eACvD;MAAI,SAAS,EAAC,aAAa;MAAA,UAAE1B,KAAK,CAAC2B;IAAW;MAAA;MAAA;MAAA;IAAA,QAAM,eACpD;MAAI,SAAS,EAAC,aAAa;MAAA,UACxB3B,KAAK,CAAC4B,YAAY,iBACjB,QAAC,eAAe;QAAC,IAAI,EAAE,CAAC,KAAK,EAAE,cAAc;MAAE;QAAA;QAAA;QAAA;MAAA;IAAG;MAAA;MAAA;MAAA;IAAA,QAEjD,eACL;MAAI,SAAS,EAAC,aAAa;MAAA,UACxB5B,KAAK,CAAC6B,WAAW,iBAChB,QAAC,eAAe;QAAC,IAAI,EAAE,CAAC,KAAK,EAAE,cAAc;MAAE;QAAA;QAAA;QAAA;MAAA;IAAG;MAAA;MAAA;MAAA;IAAA,QAEjD;EAAA,GA3BE7B,KAAK,CAACuB,GAAG;IAAA;IAAA;IAAA;EAAA,QA4Bb;AAET;AAAC,GArDexB,aAAa;AAAA,KAAbA,aAAa;AAAA;AAAA"}, "metadata": {}, "sourceType": "module"}