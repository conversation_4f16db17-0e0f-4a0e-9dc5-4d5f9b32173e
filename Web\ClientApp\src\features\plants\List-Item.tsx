import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Plant } from "api/models/plants";
import { routes } from "app/routes";
import { Link } from "react-router-dom";

export type PlantListItemProps = {
  plant: Plant;
}

export function PlantListItem({plant}: PlantListItemProps) {
  return (
    <tr key={plant._id}>
      <td>
        <Link to={routes.plants.routes.detail.to(plant._id)}>
          <FontAwesomeIcon icon={['fat', 'edit']} />
        </Link>
      </td>
      <td>{plant.abbreviation}</td>
      <td>{plant.name}</td>
      <td className="text-center">{plant.cuttingsPerPot}</td>
      <td className="text-center">{plant.potsPerCase}</td>
      <td className="text-center">
        {plant.hasLightsOut &&
          <FontAwesomeIcon icon={['fat', 'check-square']} />
        }
      </td>
      <td className="text-center">
        {plant.hasPinching &&
          <FontAwesomeIcon icon={['fat', 'check-square']} />
        }
      </td>
    </tr>
  )
}