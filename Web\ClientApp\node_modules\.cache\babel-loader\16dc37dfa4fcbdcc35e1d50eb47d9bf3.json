{"ast": null, "code": "import { createSlice } from '@reduxjs/toolkit';\nimport { sortBy, sortSizeName } from 'utils/sort';\nconst initialState = {\n  plants: []\n};\nexport const plantsSlice = createSlice({\n  name: 'plants',\n  initialState,\n  reducers: {\n    setPlants(state, action) {\n      state.plants = action.payload;\n    }\n  },\n  extraReducers: builder => {\n    builder.addCase(savePlants.fulfilled, (state, action) => {\n      // Update the plants in state with the saved versions (which have updated _rev values)\n      const savedPlants = action.payload;\n      state.plants = state.plants.map(plant => {\n        const savedPlant = savedPlants.find(sp => sp._id === plant._id);\n        return savedPlant || plant;\n      });\n    }).addCase(generateAndSaveSortOrder.fulfilled, (state, action) => {\n      // Replace all plants with the updated versions that have sort orders\n      state.plants = action.payload;\n    }).addCase(moveItemAndSave.fulfilled, (state, action) => {\n      // Replace all plants with the updated versions after move and save\n      state.plants = action.payload;\n    });\n  }\n});\nexport const {\n  setPlants\n} = plantsSlice.actions;\nexport const selectPlants = state => state.plants.plants.map(p => ({\n  ...p\n})).sort(sortPlant);\nexport default plantsSlice.reducer;\nconst sortByCrop = sortBy('crop');\nfunction sortPlant(a, b) {\n  return sortByCrop(a, b) || sortSizeName(a.size, b.size);\n}", "map": {"version": 3, "names": ["createSlice", "sortBy", "sortSizeName", "initialState", "plants", "plantsSlice", "name", "reducers", "setPlants", "state", "action", "payload", "extraReducers", "builder", "addCase", "savePlants", "fulfilled", "savedPlants", "map", "plant", "savedPlant", "find", "sp", "_id", "generateAndSaveSortOrder", "moveItemAndSave", "actions", "selectPlants", "p", "sort", "sortPlant", "reducer", "sortByCrop", "a", "b", "size"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/plants/plants-slice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from '@reduxjs/toolkit';\r\nimport { Plant } from 'api/models/plants';\r\nimport { RootState } from 'app/store';\r\nimport { sortBy, sortSizeName } from 'utils/sort';\r\n\r\nexport interface PlantsState {\r\n  plants: Plant[];\r\n}\r\n\r\nconst initialState: PlantsState = {\r\n  plants: []\r\n};\r\n\r\n\r\n\r\nexport const plantsSlice = createSlice({\r\n  name: 'plants',\r\n  initialState,\r\n  reducers: {\r\n    setPlants(state, action: PayloadAction<Plant[]>) {\r\n      state.plants = action.payload;\r\n    }\r\n  },\r\n  extraReducers: (builder) => {\r\n    builder\r\n      .addCase(savePlants.fulfilled, (state, action) => {\r\n        // Update the plants in state with the saved versions (which have updated _rev values)\r\n        const savedPlants = action.payload;\r\n        state.plants = state.plants.map(plant => {\r\n          const savedPlant = savedPlants.find(sp => sp._id === plant._id);\r\n          return savedPlant || plant;\r\n        });\r\n      })\r\n      .addCase(generateAndSaveSortOrder.fulfilled, (state, action) => {\r\n        // Replace all plants with the updated versions that have sort orders\r\n        state.plants = action.payload;\r\n      })\r\n      .addCase(moveItemAndSave.fulfilled, (state, action) => {\r\n        // Replace all plants with the updated versions after move and save\r\n        state.plants = action.payload;\r\n      });\r\n  }\r\n});\r\n\r\nexport const { setPlants } = plantsSlice.actions;\r\n\r\nexport const selectPlants = (state: RootState) => state.plants.plants.map(p => ({...p})).sort(sortPlant);\r\n\r\nexport default plantsSlice.reducer;\r\n\r\nconst sortByCrop = sortBy('crop');\r\n\r\nfunction sortPlant(a: Plant, b: Plant) {\r\n  return sortByCrop(a, b) || sortSizeName(a.size, b.size);\r\n}"], "mappings": "AAAA,SAASA,WAAW,QAAuB,kBAAkB;AAG7D,SAASC,MAAM,EAAEC,YAAY,QAAQ,YAAY;AAMjD,MAAMC,YAAyB,GAAG;EAChCC,MAAM,EAAE;AACV,CAAC;AAID,OAAO,MAAMC,WAAW,GAAGL,WAAW,CAAC;EACrCM,IAAI,EAAE,QAAQ;EACdH,YAAY;EACZI,QAAQ,EAAE;IACRC,SAAS,CAACC,KAAK,EAAEC,MAA8B,EAAE;MAC/CD,KAAK,CAACL,MAAM,GAAGM,MAAM,CAACC,OAAO;IAC/B;EACF,CAAC;EACDC,aAAa,EAAGC,OAAO,IAAK;IAC1BA,OAAO,CACJC,OAAO,CAACC,UAAU,CAACC,SAAS,EAAE,CAACP,KAAK,EAAEC,MAAM,KAAK;MAChD;MACA,MAAMO,WAAW,GAAGP,MAAM,CAACC,OAAO;MAClCF,KAAK,CAACL,MAAM,GAAGK,KAAK,CAACL,MAAM,CAACc,GAAG,CAACC,KAAK,IAAI;QACvC,MAAMC,UAAU,GAAGH,WAAW,CAACI,IAAI,CAACC,EAAE,IAAIA,EAAE,CAACC,GAAG,KAAKJ,KAAK,CAACI,GAAG,CAAC;QAC/D,OAAOH,UAAU,IAAID,KAAK;MAC5B,CAAC,CAAC;IACJ,CAAC,CAAC,CACDL,OAAO,CAACU,wBAAwB,CAACR,SAAS,EAAE,CAACP,KAAK,EAAEC,MAAM,KAAK;MAC9D;MACAD,KAAK,CAACL,MAAM,GAAGM,MAAM,CAACC,OAAO;IAC/B,CAAC,CAAC,CACDG,OAAO,CAACW,eAAe,CAACT,SAAS,EAAE,CAACP,KAAK,EAAEC,MAAM,KAAK;MACrD;MACAD,KAAK,CAACL,MAAM,GAAGM,MAAM,CAACC,OAAO;IAC/B,CAAC,CAAC;EACN;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEH;AAAU,CAAC,GAAGH,WAAW,CAACqB,OAAO;AAEhD,OAAO,MAAMC,YAAY,GAAIlB,KAAgB,IAAKA,KAAK,CAACL,MAAM,CAACA,MAAM,CAACc,GAAG,CAACU,CAAC,KAAK;EAAC,GAAGA;AAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAACC,SAAS,CAAC;AAExG,eAAezB,WAAW,CAAC0B,OAAO;AAElC,MAAMC,UAAU,GAAG/B,MAAM,CAAC,MAAM,CAAC;AAEjC,SAAS6B,SAAS,CAACG,CAAQ,EAAEC,CAAQ,EAAE;EACrC,OAAOF,UAAU,CAACC,CAAC,EAAEC,CAAC,CAAC,IAAIhC,YAAY,CAAC+B,CAAC,CAACE,IAAI,EAAED,CAAC,CAACC,IAAI,CAAC;AACzD"}, "metadata": {}, "sourceType": "module"}