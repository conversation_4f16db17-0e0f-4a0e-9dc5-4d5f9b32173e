{"ast": null, "code": "import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';\nimport { plantApi } from 'api/plant-service';\nimport { sortBy, sortSizeName } from 'utils/sort';\nconst initialState = {\n  plants: []\n};\nexport const savePlants = createAsyncThunk('plants/save-plants', async (plants, _ref) => {\n  let {\n    rejectWithValue\n  } = _ref;\n  try {\n    const updatedPlants = await plantApi.saveAll(plants);\n    return updatedPlants;\n  } catch (e) {\n    return rejectWithValue(e);\n  }\n});\nexport const generateAndSaveSortOrder = createAsyncThunk('plants/generate-and-save-sort-order', async (_, _ref2) => {\n  let {\n    getState,\n    rejectWithValue\n  } = _ref2;\n  try {\n    const state = getState();\n    const plants = state.plants.plants;\n\n    // Check if all plants have null stickingSortOrder\n    const allHaveNullSortOrder = plants.every(p => p.stickingSortOrder == null);\n    if (allHaveNullSortOrder) {\n      // Sort plants by the default sort order and assign stickingSortOrder\n      const sortedPlants = [...plants].sort(sortPlant);\n      const plantsWithSortOrder = sortedPlants.map((plant, index) => ({\n        ...plant,\n        stickingSortOrder: index\n      }));\n\n      // Save all plants with their new sort order\n      const updatedPlants = await plantApi.saveAll(plantsWithSortOrder);\n      return updatedPlants;\n    }\n\n    // If not all have null sort order, just return the current plants\n    return plants;\n  } catch (e) {\n    return rejectWithValue(e);\n  }\n});\nexport const moveItemAndSave = createAsyncThunk('plants/move-item-and-save', async (_ref3, _ref4) => {\n  let {\n    existingItem,\n    movingItem\n  } = _ref3;\n  let {\n    getState,\n    rejectWithValue\n  } = _ref4;\n  try {\n    const state = getState();\n    let plants = [...state.plants.plants];\n\n    // If existingItem has null stickingSortOrder, generate sort orders for all plants first\n    if (existingItem.stickingSortOrder == null) {\n      plants = plants.sort(sortPlant).map((p, index) => ({\n        ...p,\n        stickingSortOrder: index\n      }));\n    }\n    const existingItemIndex = plants.findIndex(p => p._id === existingItem._id);\n\n    // Move the dropped item to the existing item, and move the existing item and all subsequent items up by 1\n    const updatedPlants = plants.map(p => {\n      if (p._id === existingItem._id) {\n        return {\n          ...p,\n          stickingSortOrder: existingItemIndex + 1\n        };\n      } else if (p._id === movingItem._id) {\n        return {\n          ...p,\n          stickingSortOrder: existingItemIndex\n        };\n      } else if (p.stickingSortOrder != null && p.stickingSortOrder >= existingItemIndex) {\n        return {\n          ...p,\n          stickingSortOrder: p.stickingSortOrder + 1\n        };\n      }\n      return p;\n    });\n\n    // Save all the updated plants\n    const savedPlants = await plantApi.saveAll(updatedPlants);\n    return savedPlants;\n  } catch (e) {\n    return rejectWithValue(e);\n  }\n});\nexport const plantsSlice = createSlice({\n  name: 'plants',\n  initialState,\n  reducers: {\n    setPlants(state, action) {\n      state.plants = action.payload;\n    }\n  },\n  extraReducers: builder => {\n    builder.addCase(savePlants.fulfilled, (state, action) => {\n      // Update the plants in state with the saved versions (which have updated _rev values)\n      const savedPlants = action.payload;\n      state.plants = state.plants.map(plant => {\n        const savedPlant = savedPlants.find(sp => sp._id === plant._id);\n        return savedPlant || plant;\n      });\n    }).addCase(generateAndSaveSortOrder.fulfilled, (state, action) => {\n      // Replace all plants with the updated versions that have sort orders\n      state.plants = action.payload;\n    }).addCase(moveItemAndSave.fulfilled, (state, action) => {\n      // Replace all plants with the updated versions after move and save\n      state.plants = action.payload;\n    });\n  }\n});\nexport const {\n  setPlants\n} = plantsSlice.actions;\nexport const selectPlants = state => state.plants.plants.map(p => ({\n  ...p\n})).sort(sortPlant);\nexport default plantsSlice.reducer;\nconst sortByCrop = sortBy('crop');\nfunction sortPlant(a, b) {\n  return sortByCrop(a, b) || sortSizeName(a.size, b.size);\n}", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "plantApi", "sortBy", "sortSizeName", "initialState", "plants", "savePlants", "rejectWithValue", "updatedPlants", "saveAll", "e", "generateAndSaveSortOrder", "_", "getState", "state", "allHaveNullSortOrder", "every", "p", "stickingSortOrder", "sortedPlants", "sort", "sortPlant", "plantsWithSortOrder", "map", "plant", "index", "moveItemAndSave", "existingItem", "movingItem", "existingItemIndex", "findIndex", "_id", "savedPlants", "plantsSlice", "name", "reducers", "setPlants", "action", "payload", "extraReducers", "builder", "addCase", "fulfilled", "savedPlant", "find", "sp", "actions", "selectPlants", "reducer", "sortByCrop", "a", "b", "size"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/plants/plants-slice.ts"], "sourcesContent": ["import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';\r\nimport { Plant } from 'api/models/plants';\r\nimport { plantApi } from 'api/plant-service';\r\nimport { RootState } from 'app/store';\r\nimport { sortBy, sortSizeName } from 'utils/sort';\r\nimport { ProblemDetails } from 'utils/problem-details';\r\n\r\nexport interface PlantsState {\r\n  plants: Plant[];\r\n}\r\n\r\nconst initialState: PlantsState = {\r\n  plants: []\r\n};\r\n\r\nexport const savePlants = createAsyncThunk<Plant[], Plant[], { state: RootState }>(\r\n  'plants/save-plants',\r\n  async (plants, { rejectWithValue }) => {\r\n    try {\r\n      const updatedPlants = await plantApi.saveAll(plants);\r\n      return updatedPlants;\r\n    } catch (e) {\r\n      return rejectWithValue(e as ProblemDetails);\r\n    }\r\n  }\r\n);\r\n\r\nexport const generateAndSaveSortOrder = createAsyncThunk<Plant[], void, { state: RootState }>(\r\n  'plants/generate-and-save-sort-order',\r\n  async (_, { getState, rejectWithValue }) => {\r\n    try {\r\n      const state = getState();\r\n      const plants = state.plants.plants;\r\n\r\n      // Check if all plants have null stickingSortOrder\r\n      const allHaveNullSortOrder = plants.every(p => p.stickingSortOrder == null);\r\n\r\n      if (allHaveNullSortOrder) {\r\n        // Sort plants by the default sort order and assign stickingSortOrder\r\n        const sortedPlants = [...plants].sort(sortPlant);\r\n        const plantsWithSortOrder = sortedPlants.map((plant, index) => ({\r\n          ...plant,\r\n          stickingSortOrder: index\r\n        }));\r\n\r\n        // Save all plants with their new sort order\r\n        const updatedPlants = await plantApi.saveAll(plantsWithSortOrder);\r\n        return updatedPlants;\r\n      }\r\n\r\n      // If not all have null sort order, just return the current plants\r\n      return plants;\r\n    } catch (e) {\r\n      return rejectWithValue(e as ProblemDetails);\r\n    }\r\n  }\r\n);\r\n\r\nexport const moveItemAndSave = createAsyncThunk<Plant[], { existingItem: Plant, movingItem: Plant }, { state: RootState }>(\r\n  'plants/move-item-and-save',\r\n  async ({ existingItem, movingItem }, { getState, rejectWithValue }) => {\r\n    try {\r\n      const state = getState();\r\n      let plants = [...state.plants.plants];\r\n\r\n      // If existingItem has null stickingSortOrder, generate sort orders for all plants first\r\n      if (existingItem.stickingSortOrder == null) {\r\n        plants = plants.sort(sortPlant).map((p, index) => ({ ...p, stickingSortOrder: index }));\r\n      }\r\n\r\n      const existingItemIndex = plants.findIndex(p => p._id === existingItem._id);\r\n\r\n      // Move the dropped item to the existing item, and move the existing item and all subsequent items up by 1\r\n      const updatedPlants = plants.map(p => {\r\n        if (p._id === existingItem._id) {\r\n          return { ...p, stickingSortOrder: existingItemIndex + 1 };\r\n        } else if (p._id === movingItem._id) {\r\n          return { ...p, stickingSortOrder: existingItemIndex };\r\n        } else if (p.stickingSortOrder != null && p.stickingSortOrder >= existingItemIndex) {\r\n          return { ...p, stickingSortOrder: p.stickingSortOrder + 1 };\r\n        }\r\n        return p;\r\n      });\r\n\r\n      // Save all the updated plants\r\n      const savedPlants = await plantApi.saveAll(updatedPlants);\r\n      return savedPlants;\r\n    } catch (e) {\r\n      return rejectWithValue(e as ProblemDetails);\r\n    }\r\n  }\r\n);\r\n\r\nexport const plantsSlice = createSlice({\r\n  name: 'plants',\r\n  initialState,\r\n  reducers: {\r\n    setPlants(state, action: PayloadAction<Plant[]>) {\r\n      state.plants = action.payload;\r\n    }\r\n  },\r\n  extraReducers: (builder) => {\r\n    builder\r\n      .addCase(savePlants.fulfilled, (state, action) => {\r\n        // Update the plants in state with the saved versions (which have updated _rev values)\r\n        const savedPlants = action.payload;\r\n        state.plants = state.plants.map(plant => {\r\n          const savedPlant = savedPlants.find(sp => sp._id === plant._id);\r\n          return savedPlant || plant;\r\n        });\r\n      })\r\n      .addCase(generateAndSaveSortOrder.fulfilled, (state, action) => {\r\n        // Replace all plants with the updated versions that have sort orders\r\n        state.plants = action.payload;\r\n      })\r\n      .addCase(moveItemAndSave.fulfilled, (state, action) => {\r\n        // Replace all plants with the updated versions after move and save\r\n        state.plants = action.payload;\r\n      });\r\n  }\r\n});\r\n\r\nexport const { setPlants } = plantsSlice.actions;\r\n\r\nexport const selectPlants = (state: RootState) => state.plants.plants.map(p => ({...p})).sort(sortPlant);\r\n\r\nexport default plantsSlice.reducer;\r\n\r\nconst sortByCrop = sortBy('crop');\r\n\r\nfunction sortPlant(a: Plant, b: Plant) {\r\n  return sortByCrop(a, b) || sortSizeName(a.size, b.size);\r\n}"], "mappings": "AAAA,SAASA,WAAW,EAAiBC,gBAAgB,QAAQ,kBAAkB;AAE/E,SAASC,QAAQ,QAAQ,mBAAmB;AAE5C,SAASC,MAAM,EAAEC,YAAY,QAAQ,YAAY;AAOjD,MAAMC,YAAyB,GAAG;EAChCC,MAAM,EAAE;AACV,CAAC;AAED,OAAO,MAAMC,UAAU,GAAGN,gBAAgB,CACxC,oBAAoB,EACpB,OAAOK,MAAM,WAA0B;EAAA,IAAxB;IAAEE;EAAgB,CAAC;EAChC,IAAI;IACF,MAAMC,aAAa,GAAG,MAAMP,QAAQ,CAACQ,OAAO,CAACJ,MAAM,CAAC;IACpD,OAAOG,aAAa;EACtB,CAAC,CAAC,OAAOE,CAAC,EAAE;IACV,OAAOH,eAAe,CAACG,CAAC,CAAmB;EAC7C;AACF,CAAC,CACF;AAED,OAAO,MAAMC,wBAAwB,GAAGX,gBAAgB,CACtD,qCAAqC,EACrC,OAAOY,CAAC,YAAoC;EAAA,IAAlC;IAAEC,QAAQ;IAAEN;EAAgB,CAAC;EACrC,IAAI;IACF,MAAMO,KAAK,GAAGD,QAAQ,EAAE;IACxB,MAAMR,MAAM,GAAGS,KAAK,CAACT,MAAM,CAACA,MAAM;;IAElC;IACA,MAAMU,oBAAoB,GAAGV,MAAM,CAACW,KAAK,CAACC,CAAC,IAAIA,CAAC,CAACC,iBAAiB,IAAI,IAAI,CAAC;IAE3E,IAAIH,oBAAoB,EAAE;MACxB;MACA,MAAMI,YAAY,GAAG,CAAC,GAAGd,MAAM,CAAC,CAACe,IAAI,CAACC,SAAS,CAAC;MAChD,MAAMC,mBAAmB,GAAGH,YAAY,CAACI,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,MAAM;QAC9D,GAAGD,KAAK;QACRN,iBAAiB,EAAEO;MACrB,CAAC,CAAC,CAAC;;MAEH;MACA,MAAMjB,aAAa,GAAG,MAAMP,QAAQ,CAACQ,OAAO,CAACa,mBAAmB,CAAC;MACjE,OAAOd,aAAa;IACtB;;IAEA;IACA,OAAOH,MAAM;EACf,CAAC,CAAC,OAAOK,CAAC,EAAE;IACV,OAAOH,eAAe,CAACG,CAAC,CAAmB;EAC7C;AACF,CAAC,CACF;AAED,OAAO,MAAMgB,eAAe,GAAG1B,gBAAgB,CAC7C,2BAA2B,EAC3B,wBAAuE;EAAA,IAAhE;IAAE2B,YAAY;IAAEC;EAAW,CAAC;EAAA,IAAE;IAAEf,QAAQ;IAAEN;EAAgB,CAAC;EAChE,IAAI;IACF,MAAMO,KAAK,GAAGD,QAAQ,EAAE;IACxB,IAAIR,MAAM,GAAG,CAAC,GAAGS,KAAK,CAACT,MAAM,CAACA,MAAM,CAAC;;IAErC;IACA,IAAIsB,YAAY,CAACT,iBAAiB,IAAI,IAAI,EAAE;MAC1Cb,MAAM,GAAGA,MAAM,CAACe,IAAI,CAACC,SAAS,CAAC,CAACE,GAAG,CAAC,CAACN,CAAC,EAAEQ,KAAK,MAAM;QAAE,GAAGR,CAAC;QAAEC,iBAAiB,EAAEO;MAAM,CAAC,CAAC,CAAC;IACzF;IAEA,MAAMI,iBAAiB,GAAGxB,MAAM,CAACyB,SAAS,CAACb,CAAC,IAAIA,CAAC,CAACc,GAAG,KAAKJ,YAAY,CAACI,GAAG,CAAC;;IAE3E;IACA,MAAMvB,aAAa,GAAGH,MAAM,CAACkB,GAAG,CAACN,CAAC,IAAI;MACpC,IAAIA,CAAC,CAACc,GAAG,KAAKJ,YAAY,CAACI,GAAG,EAAE;QAC9B,OAAO;UAAE,GAAGd,CAAC;UAAEC,iBAAiB,EAAEW,iBAAiB,GAAG;QAAE,CAAC;MAC3D,CAAC,MAAM,IAAIZ,CAAC,CAACc,GAAG,KAAKH,UAAU,CAACG,GAAG,EAAE;QACnC,OAAO;UAAE,GAAGd,CAAC;UAAEC,iBAAiB,EAAEW;QAAkB,CAAC;MACvD,CAAC,MAAM,IAAIZ,CAAC,CAACC,iBAAiB,IAAI,IAAI,IAAID,CAAC,CAACC,iBAAiB,IAAIW,iBAAiB,EAAE;QAClF,OAAO;UAAE,GAAGZ,CAAC;UAAEC,iBAAiB,EAAED,CAAC,CAACC,iBAAiB,GAAG;QAAE,CAAC;MAC7D;MACA,OAAOD,CAAC;IACV,CAAC,CAAC;;IAEF;IACA,MAAMe,WAAW,GAAG,MAAM/B,QAAQ,CAACQ,OAAO,CAACD,aAAa,CAAC;IACzD,OAAOwB,WAAW;EACpB,CAAC,CAAC,OAAOtB,CAAC,EAAE;IACV,OAAOH,eAAe,CAACG,CAAC,CAAmB;EAC7C;AACF,CAAC,CACF;AAED,OAAO,MAAMuB,WAAW,GAAGlC,WAAW,CAAC;EACrCmC,IAAI,EAAE,QAAQ;EACd9B,YAAY;EACZ+B,QAAQ,EAAE;IACRC,SAAS,CAACtB,KAAK,EAAEuB,MAA8B,EAAE;MAC/CvB,KAAK,CAACT,MAAM,GAAGgC,MAAM,CAACC,OAAO;IAC/B;EACF,CAAC;EACDC,aAAa,EAAGC,OAAO,IAAK;IAC1BA,OAAO,CACJC,OAAO,CAACnC,UAAU,CAACoC,SAAS,EAAE,CAAC5B,KAAK,EAAEuB,MAAM,KAAK;MAChD;MACA,MAAML,WAAW,GAAGK,MAAM,CAACC,OAAO;MAClCxB,KAAK,CAACT,MAAM,GAAGS,KAAK,CAACT,MAAM,CAACkB,GAAG,CAACC,KAAK,IAAI;QACvC,MAAMmB,UAAU,GAAGX,WAAW,CAACY,IAAI,CAACC,EAAE,IAAIA,EAAE,CAACd,GAAG,KAAKP,KAAK,CAACO,GAAG,CAAC;QAC/D,OAAOY,UAAU,IAAInB,KAAK;MAC5B,CAAC,CAAC;IACJ,CAAC,CAAC,CACDiB,OAAO,CAAC9B,wBAAwB,CAAC+B,SAAS,EAAE,CAAC5B,KAAK,EAAEuB,MAAM,KAAK;MAC9D;MACAvB,KAAK,CAACT,MAAM,GAAGgC,MAAM,CAACC,OAAO;IAC/B,CAAC,CAAC,CACDG,OAAO,CAACf,eAAe,CAACgB,SAAS,EAAE,CAAC5B,KAAK,EAAEuB,MAAM,KAAK;MACrD;MACAvB,KAAK,CAACT,MAAM,GAAGgC,MAAM,CAACC,OAAO;IAC/B,CAAC,CAAC;EACN;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEF;AAAU,CAAC,GAAGH,WAAW,CAACa,OAAO;AAEhD,OAAO,MAAMC,YAAY,GAAIjC,KAAgB,IAAKA,KAAK,CAACT,MAAM,CAACA,MAAM,CAACkB,GAAG,CAACN,CAAC,KAAK;EAAC,GAAGA;AAAC,CAAC,CAAC,CAAC,CAACG,IAAI,CAACC,SAAS,CAAC;AAExG,eAAeY,WAAW,CAACe,OAAO;AAElC,MAAMC,UAAU,GAAG/C,MAAM,CAAC,MAAM,CAAC;AAEjC,SAASmB,SAAS,CAAC6B,CAAQ,EAAEC,CAAQ,EAAE;EACrC,OAAOF,UAAU,CAACC,CAAC,EAAEC,CAAC,CAAC,IAAIhD,YAAY,CAAC+C,CAAC,CAACE,IAAI,EAAED,CAAC,CAACC,IAAI,CAAC;AACzD"}, "metadata": {}, "sourceType": "module"}