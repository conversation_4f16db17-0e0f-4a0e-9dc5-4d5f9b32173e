{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\GreenRP\\\\Web\\\\ClientApp\\\\src\\\\features\\\\plants\\\\Detail.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { useParams, useNavigate } from 'react-router';\nimport { Link } from 'react-router-dom';\nimport { Button, FormGroup, Input, InputGroup, InputGroupText, Label, UncontrolledTooltip, Dropdown, DropdownToggle, DropdownMenu, DropdownItem } from 'reactstrap';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { routes } from 'app/routes';\nimport { useAuth } from 'features/auth/use-auth';\nimport { selectPlants } from './plants-slice';\nimport { deletePlant, savePlant, selectPlant, setPlant } from './detail-slice';\nimport { createPlant } from 'api/models/plants';\nimport { handleFocus } from 'utils/focus';\nimport { selectColours } from 'features/colours/colours-slice';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport function Detail() {\n  _s();\n  var _plant$varieties;\n  const dispatch = useDispatch(),\n    navigate = useNavigate(),\n    {\n      isInRole\n    } = useAuth(),\n    {\n      id\n    } = useParams(),\n    plants = useSelector(selectPlants),\n    plant = useSelector(selectPlant),\n    colours = useSelector(selectColours),\n    [hasVarieties, setHasVarieties] = useState(false),\n    [newVariety, setNewVariety] = useState(''),\n    [varietyDropdownOpen, setVarietyDropdownOpen] = useState({}),\n    isNew = !plant._rev,\n    canUpdate = isNew && isInRole('create:plants') || isInRole('update:plants'),\n    canDelete = isInRole('delete:plants');\n  useEffect(() => {\n    const found = plants.find(p => p._id === id);\n    if (found && found._id !== plant._id) {\n      dispatch(setPlant(found));\n      setHasVarieties(!!found.varieties);\n      setNewVariety('');\n    } else if (id === 'new' && plant._rev) {\n      dispatch(setPlant(createPlant()));\n      setHasVarieties(false);\n      setNewVariety('');\n    }\n  }, [dispatch, id, plant, plants]);\n  useEffect(() => {\n    return function cleanup() {\n      dispatch(setPlant(createPlant()));\n    };\n  }, [dispatch]);\n  const handleSizeChange = e => {\n    const size = e.target.value,\n      name = `${size} ${plant.crop}`,\n      update = {\n        ...plant,\n        size,\n        name\n      };\n    dispatch(setPlant(update));\n  };\n  const handleCropChange = e => {\n    const crop = e.target.value,\n      name = `${plant.size} ${crop}`,\n      update = {\n        ...plant,\n        crop,\n        name\n      };\n    dispatch(setPlant(update));\n  };\n  const handleAbbreviationChange = e => {\n    const abbreviation = e.target.value,\n      update = {\n        ...plant,\n        abbreviation\n      };\n    dispatch(setPlant(update));\n  };\n  const handleCuttingsPerPotChange = e => {\n    const cuttingsPerPot = e.target.valueAsNumber,\n      update = {\n        ...plant,\n        cuttingsPerPot\n      };\n    dispatch(setPlant(update));\n  };\n  const handleDefaultStickingCrewSizeChange = e => {\n    const defaultStickingCrewSize = e.target.valueAsNumber,\n      update = {\n        ...plant,\n        defaultStickingCrewSize\n      };\n    dispatch(setPlant(update));\n  };\n  const handleCuttingsPerTableTightChange = e => {\n    const cuttingsPerTableTight = e.target.valueAsNumber,\n      update = {\n        ...plant,\n        cuttingsPerTableTight\n      };\n    dispatch(setPlant(update));\n  };\n  const handleCuttingsPerTableSpacedChange = e => {\n    const cuttingsPerTableSpaced = e.target.valueAsNumber,\n      update = {\n        ...plant,\n        cuttingsPerTableSpaced\n      };\n    dispatch(setPlant(update));\n  };\n  const handleCuttingsPerTablePartiallySpacedChange = e => {\n    const cuttingsPerTablePartiallySpaced = e.target.valueAsNumber,\n      update = {\n        ...plant,\n        cuttingsPerTablePartiallySpaced\n      };\n    dispatch(setPlant(update));\n  };\n  const handlePotsPerCaseChange = e => {\n    const potsPerCase = e.target.valueAsNumber,\n      update = {\n        ...plant,\n        potsPerCase\n      };\n    dispatch(setPlant(update));\n  };\n  const handleHasLightsOutChange = e => {\n    const hasLightsOut = e.target.checked,\n      update = {\n        ...plant,\n        hasLightsOut\n      };\n    dispatch(setPlant(update));\n  };\n  const handleHasPinchingChange = e => {\n    const hasPinching = e.target.checked,\n      update = {\n        ...plant,\n        hasPinching\n      };\n    if (!hasPinching) {\n      update.pinchingPotsPerHour = 0;\n      delete update.daysToPinch;\n    } else {\n      if (!update.daysToPinch) {\n        update.daysToPinch = 1;\n      }\n    }\n    dispatch(setPlant(update));\n  };\n  const handleDaysToPinchChange = e => {\n    const daysToPinch = e.target.valueAsNumber || 0,\n      update = {\n        ...plant,\n        daysToPinch\n      };\n    dispatch(setPlant(update));\n  };\n  const handlePinchingingPotsPerHourChange = e => {\n    const pinchingPotsPerHour = e.target.valueAsNumber,\n      update = {\n        ...plant,\n        pinchingPotsPerHour\n      };\n    dispatch(setPlant(update));\n  };\n  const handleColourChange = e => {\n    const colour = e.target.value,\n      update = {\n        ...plant\n      };\n    if (colour) {\n      update.colour = colour;\n    } else {\n      delete plant.colour;\n    }\n    dispatch(setPlant(update));\n  };\n  const handleClearColourClick = () => {\n    const update = {\n      ...plant\n    };\n    delete update.colour;\n    dispatch(setPlant(update));\n  };\n  const handleAddColourClick = () => {\n    const update = {\n      ...plant,\n      colour: '#ffffff'\n    };\n    dispatch(setPlant(update));\n  };\n  const handleHasVarietiesChange = e => {\n    const hasVarieties = e.target.checked,\n      update = {\n        ...plant\n      };\n    if (hasVarieties) {\n      update.varieties = [];\n    } else {\n      delete update.varieties;\n    }\n    dispatch(setPlant(update));\n    setHasVarieties(hasVarieties);\n  };\n  const handleStickingCuttingsPerHourChange = e => {\n    const stickingCuttingsPerHour = e.target.valueAsNumber,\n      update = {\n        ...plant,\n        stickingCuttingsPerHour\n      };\n    dispatch(setPlant(update));\n  };\n  const handleSpacingPotsPerHourChange = e => {\n    const spacingPotsPerHour = e.target.valueAsNumber,\n      update = {\n        ...plant,\n        spacingPotsPerHour\n      };\n    dispatch(setPlant(update));\n  };\n  const handlePackingCasesPerHourChange = e => {\n    const packingCasesPerHour = e.target.valueAsNumber,\n      update = {\n        ...plant,\n        packingCasesPerHour\n      };\n    dispatch(setPlant(update));\n  };\n  const handleNewVarietyChange = e => {\n    const newVariety = e.target.value;\n    setNewVariety(newVariety);\n  };\n  const handleNewVarietyKeyUp = e => {\n    if (newVariety && e.key === 'Enter') {\n      handleAddNewVarietyClick();\n    }\n  };\n  const handleAddNewVarietyClick = () => {\n    if (newVariety) {\n      const update = {\n          ...plant\n        },\n        varieties = (update.varieties || []).map(v => ({\n          ...v\n        }));\n      varieties.push({\n        name: newVariety,\n        stickingSortOrder: null\n      });\n      update.varieties = varieties;\n      dispatch(setPlant(update));\n      setNewVariety('');\n    }\n  };\n  const handleVarietyNameChange = (e, variety) => {\n    const update = {\n        ...plant\n      },\n      varieties = (update.varieties || []).map(v => ({\n        ...v\n      })),\n      index = varieties.findIndex(v => v.name === variety.name);\n    if (index !== -1) {\n      const name = e.target.value,\n        updated = {\n          ...varieties[index],\n          name\n        };\n      varieties.splice(index, 1, updated);\n    }\n    update.varieties = varieties;\n    dispatch(setPlant(update));\n  };\n  const handleDeleteVarietyClick = variety => {\n    const update = {\n        ...plant\n      },\n      varieties = (update.varieties || []).map(v => ({\n        ...v\n      })),\n      index = varieties.findIndex(v => v.name === variety.name);\n    if (index !== -1) {\n      varieties.splice(index, 1);\n    }\n    update.varieties = varieties;\n    dispatch(setPlant(update));\n  };\n  const toggleVarietyDropdown = varietyName => {\n    setVarietyDropdownOpen(prev => ({\n      ...prev,\n      [varietyName]: !prev[varietyName]\n    }));\n  };\n  const handleVarietyColourSelect = (variety, colourName) => {\n    const colour = colourName ? colours.find(c => c.name === colourName) || null : null;\n    const update = {\n        ...plant\n      },\n      varieties = (update.varieties || []).map(v => ({\n        ...v\n      })),\n      index = varieties.findIndex(v => v.name === variety.name);\n    if (index !== -1) {\n      const updated = {\n        ...varieties[index],\n        colour\n      };\n      varieties.splice(index, 1, updated);\n    }\n    update.varieties = varieties;\n    dispatch(setPlant(update));\n\n    // Close the dropdown\n    setVarietyDropdownOpen(prev => ({\n      ...prev,\n      [variety.name]: false\n    }));\n  };\n  const handleSaveClick = async () => {\n    const result = await dispatch(savePlant());\n    if (!result.error) {\n      navigate(routes.plants.path);\n    }\n  };\n  const handleDeleteClick = async () => {\n    const result = await dispatch(deletePlant());\n    if (!result.error) {\n      navigate(routes.plants.path);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container d-grid gap-3\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row sticky-top-navbar my-2 py-2 bg-white shadow\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-auto pt-3\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: routes.plants.path,\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: ['fat', 'chevron-left']\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 13\n          }, this), \"\\xA0 Back to Plants List\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"col\",\n        children: isNew ? 'New Plant' : plant.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 356,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 349,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"plant-size\",\n          children: \"Size\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"plant-size\",\n          value: plant.size,\n          onChange: handleSizeChange,\n          disabled: !canUpdate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 359,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"plant-crop\",\n          children: \"Crop\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"plant-crop\",\n          value: plant.crop,\n          onChange: handleCropChange,\n          disabled: !canUpdate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 368,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"plant-abbreviation\",\n          children: \"Abbreviation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"plant-abbreviation\",\n          value: plant.abbreviation,\n          onChange: handleAbbreviationChange,\n          disabled: !canUpdate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 377,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 358,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"plant-cuttings-per-pot\",\n          children: \"Cuttings per Pot\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"plant-cuttings-per-pot\",\n          type: \"number\",\n          value: plant.cuttingsPerPot,\n          onChange: handleCuttingsPerPotChange,\n          onFocus: handleFocus,\n          disabled: !canUpdate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"plant-pots-per-case\",\n          children: \"Pots Per Case\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"plant-pots-per-case\",\n          type: \"number\",\n          value: plant.potsPerCase,\n          onChange: handlePotsPerCaseChange,\n          onFocus: handleFocus,\n          disabled: !canUpdate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 399,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"plant-default-sticking-crew-size\",\n          children: \"Default Sticking Crew Size\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"plant-default-sticking-crew-size\",\n          type: \"number\",\n          value: plant.defaultStickingCrewSize,\n          onChange: handleDefaultStickingCrewSizeChange,\n          onFocus: handleFocus,\n          disabled: !canUpdate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 410,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 387,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"plant-cuttings-per-table-tight\",\n          children: \"Cuttings Per Table: Tight\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 426,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"plant-cuttings-per-table-tight\",\n          type: \"number\",\n          value: plant.cuttingsPerTableTight,\n          onChange: handleCuttingsPerTableTightChange,\n          onFocus: handleFocus,\n          disabled: !canUpdate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 425,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"plant-cuttings-per-table-partially-spaced\",\n          children: \"Cuttings Per Table: Partially Spaced\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 439,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"plant-cuttings-per-table-partially-spaced\",\n          type: \"number\",\n          value: plant.cuttingsPerTablePartiallySpaced,\n          onChange: handleCuttingsPerTablePartiallySpacedChange,\n          onFocus: handleFocus,\n          disabled: !canUpdate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 442,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 438,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"plant-cuttings-per-table-spaced\",\n          children: \"Cuttings Per Table: Spaced\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 452,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"plant-cuttings-per-table-spaced\",\n          type: \"number\",\n          value: plant.cuttingsPerTableSpaced,\n          onChange: handleCuttingsPerTableSpacedChange,\n          onFocus: handleFocus,\n          disabled: !canUpdate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 455,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 451,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 424,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"plant-sticking-cuttings-per-hour\",\n          children: \"Sticking: Cuttings per Hour\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 467,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"plant-sticking-cuttings-per-hour\",\n          type: \"number\",\n          value: plant.stickingCuttingsPerHour,\n          onChange: handleStickingCuttingsPerHourChange,\n          onFocus: handleFocus,\n          disabled: !canUpdate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 470,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 466,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"plant-spacing-pots-per-hour\",\n          children: \"Spacing: Pots per Hour\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 480,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"plant-spacing-pots-per-hour\",\n          type: \"number\",\n          value: plant.spacingPotsPerHour,\n          onChange: handleSpacingPotsPerHourChange,\n          onFocus: handleFocus,\n          disabled: !canUpdate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 483,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 479,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"plant-packing-cases-per-hour\",\n          children: \"Packing: Cases per Hour\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 493,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"plant-packing-cases-per-hour\",\n          type: \"number\",\n          value: plant.packingCasesPerHour,\n          onChange: handlePackingCasesPerHourChange,\n          onFocus: handleFocus,\n          disabled: !canUpdate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 496,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 492,\n        columnNumber: 9\n      }, this), !!plant.hasPinching && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"plant-pinching-pots-per-hour\",\n          children: \"Pinching: Pots per Hour\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 507,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"plant-pinching-pots-per-hour\",\n          type: \"number\",\n          value: plant.pinchingPotsPerHour,\n          onChange: handlePinchingingPotsPerHourChange,\n          onFocus: handleFocus,\n          disabled: !canUpdate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 510,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 506,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 465,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row my-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-3\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-check\",\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            id: \"plant-has-lights-out\",\n            type: \"checkbox\",\n            checked: plant.hasLightsOut,\n            onChange: handleHasLightsOutChange,\n            disabled: !canUpdate\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 524,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"plant-has-lights-out\",\n            children: \"Has Lights Out\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 531,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 523,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 522,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-check\",\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            id: \"plant-needs-pinching\",\n            type: \"checkbox\",\n            checked: plant.hasPinching,\n            onChange: handleHasPinchingChange,\n            disabled: !canUpdate\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 536,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"plant-needs-pinching\",\n            children: \"Needs Pinching\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 543,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 535,\n          columnNumber: 11\n        }, this), !!plant.hasPinching && /*#__PURE__*/_jsxDEV(FormGroup, {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"plant-pinching-pots-per-hour\",\n            children: \"Days after sticking to pinch\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 547,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            id: \"plant-pinching-pots-per-hour\",\n            type: \"number\",\n            value: plant.daysToPinch || '',\n            onChange: handleDaysToPinchChange,\n            onFocus: handleFocus,\n            disabled: !canUpdate\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 550,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 546,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 534,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"plant-colour\",\n          children: \"Colour\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 562,\n          columnNumber: 11\n        }, this), !!plant.colour && /*#__PURE__*/_jsxDEV(InputGroup, {\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            id: \"plant-colour\",\n            type: \"color\",\n            value: plant.colour || '',\n            onChange: handleColourChange,\n            disabled: !canUpdate\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 565,\n            columnNumber: 15\n          }, this), canUpdate && /*#__PURE__*/_jsxDEV(Button, {\n            size: \"sm\",\n            color: \"danger\",\n            outline: true,\n            onClick: handleClearColourClick,\n            children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n              icon: ['fat', 'trash']\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 578,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 573,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 564,\n          columnNumber: 13\n        }, this), !plant.colour && /*#__PURE__*/_jsxDEV(InputGroup, {\n          children: [/*#__PURE__*/_jsxDEV(InputGroupText, {\n            children: \"No Colour\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 585,\n            columnNumber: 15\n          }, this), canUpdate && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              id: \"add-colour\",\n              size: \"sm\",\n              color: \"success\",\n              outline: true,\n              onClick: handleAddColourClick,\n              children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: ['fat', 'palette']\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 594,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 588,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(UncontrolledTooltip, {\n              target: \"add-colour\",\n              children: \"Add Colour\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 596,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 584,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 561,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 521,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-3\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-check\",\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            id: \"plant-has-varieties\",\n            type: \"checkbox\",\n            checked: hasVarieties,\n            onChange: handleHasVarietiesChange,\n            disabled: !canUpdate\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 608,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"plant-has-varieties\",\n            children: \"Has Varieties\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 615,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 607,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 606,\n        columnNumber: 9\n      }, this), hasVarieties && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-6\",\n        children: [canUpdate && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-5\",\n            children: /*#__PURE__*/_jsxDEV(FormGroup, {\n              floating: true,\n              children: [/*#__PURE__*/_jsxDEV(Input, {\n                id: \"new-variety-name\",\n                value: newVariety,\n                onChange: handleNewVarietyChange,\n                onKeyUp: handleNewVarietyKeyUp,\n                placeholder: \"Add Variety\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 624,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Label, {\n                htmlFor: \"new-variety-name\",\n                children: \"Add Variety\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 631,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 623,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 622,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-4\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              outline: true,\n              color: \"success\",\n              onClick: handleAddNewVarietyClick,\n              disabled: !newVariety,\n              size: \"sm\",\n              children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: ['fat', 'plus']\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 641,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 635,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 634,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 621,\n          columnNumber: 15\n        }, this), (_plant$varieties = plant.varieties) === null || _plant$varieties === void 0 ? void 0 : _plant$varieties.map(variety => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-5\",\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              value: variety.name,\n              onChange: e => handleVarietyNameChange(e, variety),\n              disabled: !canUpdate\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 649,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 648,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-3\",\n            children: /*#__PURE__*/_jsxDEV(Dropdown, {\n              isOpen: varietyDropdownOpen[variety.name] || false,\n              toggle: () => toggleVarietyDropdown(variety.name),\n              disabled: !canUpdate,\n              children: [/*#__PURE__*/_jsxDEV(DropdownToggle, {\n                caret: true,\n                className: \"w-100 text-start d-flex align-items-center justify-content-between\",\n                style: {\n                  backgroundColor: 'white',\n                  borderColor: '#ced4da',\n                  color: '#495057'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex align-items-center\",\n                  children: variety.colour ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w16 h16 border rounded\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 671,\n                      columnNumber: 29\n                    }, this), variety.colour.name]\n                  }, void 0, true) : '---'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 668,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 660,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(DropdownMenu, {\n                className: \"w-100\",\n                children: [/*#__PURE__*/_jsxDEV(DropdownItem, {\n                  onClick: () => handleVarietyColourSelect(variety, ''),\n                  children: \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 682,\n                  columnNumber: 23\n                }, this), colours.map(c => /*#__PURE__*/_jsxDEV(DropdownItem, {\n                  onClick: () => handleVarietyColourSelect(variety, c.name),\n                  className: \"d-flex align-items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: '16px',\n                      height: '16px',\n                      backgroundColor: c.hex,\n                      border: '1px solid #ccc',\n                      marginRight: '8px',\n                      borderRadius: '2px'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 690,\n                    columnNumber: 27\n                  }, this), c.name]\n                }, c.name, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 686,\n                  columnNumber: 25\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 681,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 656,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 655,\n            columnNumber: 17\n          }, this), canUpdate && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-4\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              outline: true,\n              color: \"danger\",\n              onClick: () => handleDeleteVarietyClick(variety),\n              size: \"sm\",\n              children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: ['fat', 'trash-alt']\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 713,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 708,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 707,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 647,\n          columnNumber: 15\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 619,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 605,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row sticky-bottom bg-white border-top py-2\",\n      children: [!isNew && canDelete && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-auto\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleDeleteClick,\n          outline: true,\n          color: \"danger\",\n          size: \"lg\",\n          className: \"me-auto\",\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: ['fat', 'trash-alt']\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 731,\n            columnNumber: 15\n          }, this), \"\\xA0 Delete\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 725,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 724,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col text-end\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          tag: Link,\n          to: routes.plants.path,\n          outline: true,\n          size: \"lg\",\n          children: canUpdate ? 'Cancel' : 'Close'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 737,\n          columnNumber: 11\n        }, this), canUpdate && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [\"\\xA0\", /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSaveClick,\n            color: \"success\",\n            size: \"lg\",\n            children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n              icon: ['fat', 'save']\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 744,\n              columnNumber: 17\n            }, this), \"\\xA0 Save\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 743,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 736,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 722,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 348,\n    columnNumber: 5\n  }, this);\n}\n_s(Detail, \"DUE4rbRCdXEfSMB7qMV7QjgdiAs=\", false, function () {\n  return [useDispatch, useNavigate, useAuth, useParams, useSelector, useSelector, useSelector];\n});\n_c = Detail;\nexport default Detail;\nvar _c;\n$RefreshReg$(_c, \"Detail\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useSelector", "useDispatch", "useParams", "useNavigate", "Link", "<PERSON><PERSON>", "FormGroup", "Input", "InputGroup", "InputGroupText", "Label", "UncontrolledTooltip", "Dropdown", "DropdownToggle", "DropdownMenu", "DropdownItem", "FontAwesomeIcon", "routes", "useAuth", "selectPlants", "deletePlant", "savePlant", "selectPlant", "setPlant", "createPlant", "handleFocus", "selectColours", "Detail", "dispatch", "navigate", "isInRole", "id", "plants", "plant", "colours", "hasVarieties", "setHasVarieties", "newVariety", "setNewVariety", "varietyDropdownOpen", "setVarietyDropdownOpen", "isNew", "_rev", "canUpdate", "canDelete", "found", "find", "p", "_id", "varieties", "cleanup", "handleSizeChange", "e", "size", "target", "value", "name", "crop", "update", "handleCropChange", "handleAbbreviationChange", "abbreviation", "handleCuttingsPerPotChange", "cuttingsPerPot", "valueAsNumber", "handleDefaultStickingCrewSizeChange", "defaultStickingCrewSize", "handleCuttingsPerTableTightChange", "cuttingsPerTableTight", "handleCuttingsPerTableSpacedChange", "cuttingsPerTableSpaced", "handleCuttingsPerTablePartiallySpacedChange", "cuttingsPerTablePartiallySpaced", "handlePotsPerCaseChange", "potsPerCase", "handleHasLightsOutChange", "hasLightsOut", "checked", "handleHasPinchingChange", "hasPinching", "pinchingPotsPerHour", "daysToPinch", "handleDaysToPinchChange", "handlePinchingingPotsPerHourChange", "handleColourChange", "colour", "handleClearColourClick", "handleAddColourClick", "handleHasVarietiesChange", "handleStickingCuttingsPerHourChange", "stickingCuttingsPerHour", "handleSpacingPotsPerHourChange", "spacingPotsPerHour", "handlePackingCasesPerHourChange", "packingCasesPerHour", "handleNewVarietyChange", "handleNewVarietyKeyUp", "key", "handleAddNewVarietyClick", "map", "v", "push", "stickingSortOrder", "handleVarietyNameChange", "variety", "index", "findIndex", "updated", "splice", "handleDeleteVarietyClick", "toggleVarietyDropdown", "varietyName", "prev", "handleVarietyColourSelect", "colourName", "c", "handleSaveClick", "result", "error", "path", "handleDeleteClick", "backgroundColor", "borderColor", "color", "width", "height", "hex", "border", "marginRight", "borderRadius"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/plants/Detail.tsx"], "sourcesContent": ["import React, { useEffect, useState, useRef } from 'react';\r\nimport { useSelector, useDispatch } from 'react-redux';\r\nimport { useParams, useNavigate } from 'react-router';\r\nimport { Link } from 'react-router-dom';\r\nimport {\r\n  Button,\r\n  FormGroup,\r\n  Input,\r\n  InputGroup,\r\n  InputGroupText,\r\n  Label,\r\n  UncontrolledTooltip,\r\n  Dropdown,\r\n  DropdownToggle,\r\n  DropdownMenu,\r\n  DropdownItem,\r\n} from 'reactstrap';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { routes } from 'app/routes';\r\nimport { useAuth } from 'features/auth/use-auth';\r\nimport { selectPlants, generateAndSaveVarietySortOrder, moveVarietyAndSave, sortVariety } from './plants-slice';\r\nimport { deletePlant, savePlant, selectPlant, setPlant } from './detail-slice';\r\nimport { createPlant, Variety } from 'api/models/plants';\r\nimport { handleFocus } from 'utils/focus';\r\nimport { selectColours } from 'features/colours/colours-slice';\r\nimport { DndProvider, useDrag, useDrop } from 'react-dnd';\r\nimport { HTML5Backend } from 'react-dnd-html5-backend';\r\n\r\nexport function Detail() {\r\n  const dispatch = useDispatch(),\r\n    navigate = useNavigate(),\r\n    { isInRole } = useAuth(),\r\n    { id } = useParams<{ id: string }>(),\r\n    plants = useSelector(selectPlants),\r\n    plant = useSelector(selectPlant),\r\n    colours = useSelector(selectColours),\r\n    [hasVarieties, setHasVarieties] = useState(false),\r\n    [newVariety, setNewVariety] = useState(''),\r\n    [varietyDropdownOpen, setVarietyDropdownOpen] = useState<{[key: string]: boolean}>({}),\r\n    isNew = !plant._rev,\r\n    canUpdate =\r\n      (isNew && isInRole('create:plants')) || isInRole('update:plants'),\r\n    canDelete = isInRole('delete:plants');\r\n\r\n  useEffect(() => {\r\n    const found = plants.find((p) => p._id === id);\r\n    if (found && found._id !== plant._id) {\r\n      dispatch(setPlant(found));\r\n      setHasVarieties(!!found.varieties);\r\n      setNewVariety('');\r\n    } else if (id === 'new' && plant._rev) {\r\n      dispatch(setPlant(createPlant()));\r\n      setHasVarieties(false);\r\n      setNewVariety('');\r\n    }\r\n  }, [dispatch, id, plant, plants]);\r\n\r\n  useEffect(() => {\r\n    return function cleanup() {\r\n      dispatch(setPlant(createPlant()));\r\n    };\r\n  }, [dispatch]);\r\n\r\n  const handleSizeChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const size = e.target.value,\r\n      name = `${size} ${plant.crop}`,\r\n      update = { ...plant, size, name };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleCropChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const crop = e.target.value,\r\n      name = `${plant.size} ${crop}`,\r\n      update = { ...plant, crop, name };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleAbbreviationChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const abbreviation = e.target.value,\r\n      update = { ...plant, abbreviation };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleCuttingsPerPotChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    const cuttingsPerPot = e.target.valueAsNumber,\r\n      update = { ...plant, cuttingsPerPot };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleDefaultStickingCrewSizeChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    const defaultStickingCrewSize = e.target.valueAsNumber,\r\n      update = { ...plant, defaultStickingCrewSize };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleCuttingsPerTableTightChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    const cuttingsPerTableTight = e.target.valueAsNumber,\r\n      update = { ...plant, cuttingsPerTableTight };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleCuttingsPerTableSpacedChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    const cuttingsPerTableSpaced = e.target.valueAsNumber,\r\n      update = { ...plant, cuttingsPerTableSpaced };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleCuttingsPerTablePartiallySpacedChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    const cuttingsPerTablePartiallySpaced = e.target.valueAsNumber,\r\n      update = { ...plant, cuttingsPerTablePartiallySpaced };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handlePotsPerCaseChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const potsPerCase = e.target.valueAsNumber,\r\n      update = { ...plant, potsPerCase };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleHasLightsOutChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const hasLightsOut = e.target.checked,\r\n      update = { ...plant, hasLightsOut };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleHasPinchingChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const hasPinching = e.target.checked,\r\n      update = { ...plant, hasPinching };\r\n\r\n    if (!hasPinching) {\r\n      update.pinchingPotsPerHour = 0;\r\n      delete update.daysToPinch;\r\n    } else {\r\n      if (!update.daysToPinch) {\r\n        update.daysToPinch = 1;\r\n      }\r\n    }\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleDaysToPinchChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const daysToPinch = e.target.valueAsNumber || 0,\r\n      update = { ...plant, daysToPinch };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handlePinchingingPotsPerHourChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    const pinchingPotsPerHour = e.target.valueAsNumber,\r\n      update = { ...plant, pinchingPotsPerHour };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleColourChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const colour = e.target.value,\r\n      update = { ...plant };\r\n\r\n    if (colour) {\r\n      update.colour = colour;\r\n    } else {\r\n      delete plant.colour;\r\n    }\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleClearColourClick = () => {\r\n    const update = { ...plant };\r\n    delete update.colour;\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleAddColourClick = () => {\r\n    const update = { ...plant, colour: '#ffffff' };\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleHasVarietiesChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const hasVarieties = e.target.checked,\r\n      update = { ...plant };\r\n\r\n    if (hasVarieties) {\r\n      update.varieties = [];\r\n    } else {\r\n      delete update.varieties;\r\n    }\r\n\r\n    dispatch(setPlant(update));\r\n\r\n    setHasVarieties(hasVarieties);\r\n  };\r\n\r\n  const handleStickingCuttingsPerHourChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    const stickingCuttingsPerHour = e.target.valueAsNumber,\r\n      update = { ...plant, stickingCuttingsPerHour };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleSpacingPotsPerHourChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    const spacingPotsPerHour = e.target.valueAsNumber,\r\n      update = { ...plant, spacingPotsPerHour };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handlePackingCasesPerHourChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    const packingCasesPerHour = e.target.valueAsNumber,\r\n      update = { ...plant, packingCasesPerHour };\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const handleNewVarietyChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const newVariety = e.target.value;\r\n    setNewVariety(newVariety);\r\n  };\r\n\r\n  const handleNewVarietyKeyUp = (e: React.KeyboardEvent) => {\r\n    if (newVariety && e.key === 'Enter') {\r\n      handleAddNewVarietyClick();\r\n    }\r\n  };\r\n\r\n  const handleAddNewVarietyClick = () => {\r\n    if (newVariety) {\r\n      const update = { ...plant },\r\n        varieties = (update.varieties || []).map((v) => ({ ...v }));\r\n\r\n      varieties.push({ name: newVariety, stickingSortOrder: null });\r\n\r\n      update.varieties = varieties;\r\n      dispatch(setPlant(update));\r\n      setNewVariety('');\r\n    }\r\n  };\r\n\r\n  const handleVarietyNameChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>,\r\n    variety: Variety\r\n  ) => {\r\n    const update = { ...plant },\r\n      varieties = (update.varieties || []).map((v) => ({ ...v })),\r\n      index = varieties.findIndex((v) => v.name === variety.name);\r\n\r\n    if (index !== -1) {\r\n      const name = e.target.value,\r\n        updated = { ...varieties[index], name };\r\n      varieties.splice(index, 1, updated);\r\n    }\r\n\r\n    update.varieties = varieties;\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n\r\n\r\n  const handleDeleteVarietyClick = (variety: Variety) => {\r\n    const update = { ...plant },\r\n      varieties = (update.varieties || []).map((v) => ({ ...v })),\r\n      index = varieties.findIndex((v) => v.name === variety.name);\r\n\r\n    if (index !== -1) {\r\n      varieties.splice(index, 1);\r\n    }\r\n\r\n    update.varieties = varieties;\r\n\r\n    dispatch(setPlant(update));\r\n  };\r\n\r\n  const toggleVarietyDropdown = (varietyName: string) => {\r\n    setVarietyDropdownOpen(prev => ({\r\n      ...prev,\r\n      [varietyName]: !prev[varietyName]\r\n    }));\r\n  };\r\n\r\n  const handleVarietyColourSelect = (variety: Variety, colourName: string) => {\r\n    const colour = colourName ? colours.find((c) => c.name === colourName) || null : null;\r\n    const update = { ...plant },\r\n      varieties = (update.varieties || []).map((v) => ({ ...v })),\r\n      index = varieties.findIndex((v) => v.name === variety.name);\r\n\r\n    if (index !== -1) {\r\n      const updated = { ...varieties[index], colour };\r\n      varieties.splice(index, 1, updated);\r\n    }\r\n\r\n    update.varieties = varieties;\r\n    dispatch(setPlant(update));\r\n\r\n    // Close the dropdown\r\n    setVarietyDropdownOpen(prev => ({\r\n      ...prev,\r\n      [variety.name]: false\r\n    }));\r\n  };\r\n\r\n  const handleSaveClick = async () => {\r\n    const result: any = await dispatch(savePlant());\r\n\r\n    if (!result.error) {\r\n      navigate(routes.plants.path);\r\n    }\r\n  };\r\n\r\n  const handleDeleteClick = async () => {\r\n    const result: any = await dispatch(deletePlant());\r\n\r\n    if (!result.error) {\r\n      navigate(routes.plants.path);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"container d-grid gap-3\">\r\n      <div className=\"row sticky-top-navbar my-2 py-2 bg-white shadow\">\r\n        <div className=\"col-auto pt-3\">\r\n          <Link to={routes.plants.path}>\r\n            <FontAwesomeIcon icon={['fat', 'chevron-left']} />\r\n            &nbsp; Back to Plants List\r\n          </Link>\r\n        </div>\r\n        <h1 className=\"col\">{isNew ? 'New Plant' : plant.name}</h1>\r\n      </div>\r\n      <div className=\"row\">\r\n        <div className=\"col-12 col-md-3\">\r\n          <label htmlFor=\"plant-size\">Size</label>\r\n          <Input\r\n            id=\"plant-size\"\r\n            value={plant.size}\r\n            onChange={handleSizeChange}\r\n            disabled={!canUpdate}\r\n          />\r\n        </div>\r\n        <div className=\"col-12 col-md-3\">\r\n          <label htmlFor=\"plant-crop\">Crop</label>\r\n          <Input\r\n            id=\"plant-crop\"\r\n            value={plant.crop}\r\n            onChange={handleCropChange}\r\n            disabled={!canUpdate}\r\n          />\r\n        </div>\r\n        <div className=\"col-12 col-md-3\">\r\n          <label htmlFor=\"plant-abbreviation\">Abbreviation</label>\r\n          <Input\r\n            id=\"plant-abbreviation\"\r\n            value={plant.abbreviation}\r\n            onChange={handleAbbreviationChange}\r\n            disabled={!canUpdate}\r\n          />\r\n        </div>\r\n      </div>\r\n      <div className=\"row\">\r\n        <div className=\"col-12 col-md-3\">\r\n          <label htmlFor=\"plant-cuttings-per-pot\">Cuttings per Pot</label>\r\n          <Input\r\n            id=\"plant-cuttings-per-pot\"\r\n            type=\"number\"\r\n            value={plant.cuttingsPerPot}\r\n            onChange={handleCuttingsPerPotChange}\r\n            onFocus={handleFocus}\r\n            disabled={!canUpdate}\r\n          />\r\n        </div>\r\n        <div className=\"col-12 col-md-3\">\r\n          <label htmlFor=\"plant-pots-per-case\">Pots Per Case</label>\r\n          <Input\r\n            id=\"plant-pots-per-case\"\r\n            type=\"number\"\r\n            value={plant.potsPerCase}\r\n            onChange={handlePotsPerCaseChange}\r\n            onFocus={handleFocus}\r\n            disabled={!canUpdate}\r\n          />\r\n        </div>\r\n        <div className=\"col-12 col-md-3\">\r\n          <label htmlFor=\"plant-default-sticking-crew-size\">\r\n            Default Sticking Crew Size\r\n          </label>\r\n          <Input\r\n            id=\"plant-default-sticking-crew-size\"\r\n            type=\"number\"\r\n            value={plant.defaultStickingCrewSize}\r\n            onChange={handleDefaultStickingCrewSizeChange}\r\n            onFocus={handleFocus}\r\n            disabled={!canUpdate}\r\n          />\r\n        </div>\r\n      </div>\r\n      <div className=\"row\">\r\n        <div className=\"col-12 col-md-3\">\r\n          <label htmlFor=\"plant-cuttings-per-table-tight\">\r\n            Cuttings Per Table: Tight\r\n          </label>\r\n          <Input\r\n            id=\"plant-cuttings-per-table-tight\"\r\n            type=\"number\"\r\n            value={plant.cuttingsPerTableTight}\r\n            onChange={handleCuttingsPerTableTightChange}\r\n            onFocus={handleFocus}\r\n            disabled={!canUpdate}\r\n          />\r\n        </div>\r\n        <div className=\"col-12 col-md-3\">\r\n          <label htmlFor=\"plant-cuttings-per-table-partially-spaced\">\r\n            Cuttings Per Table: Partially Spaced\r\n          </label>\r\n          <Input\r\n            id=\"plant-cuttings-per-table-partially-spaced\"\r\n            type=\"number\"\r\n            value={plant.cuttingsPerTablePartiallySpaced}\r\n            onChange={handleCuttingsPerTablePartiallySpacedChange}\r\n            onFocus={handleFocus}\r\n            disabled={!canUpdate}\r\n          />\r\n        </div>\r\n        <div className=\"col-12 col-md-3\">\r\n          <label htmlFor=\"plant-cuttings-per-table-spaced\">\r\n            Cuttings Per Table: Spaced\r\n          </label>\r\n          <Input\r\n            id=\"plant-cuttings-per-table-spaced\"\r\n            type=\"number\"\r\n            value={plant.cuttingsPerTableSpaced}\r\n            onChange={handleCuttingsPerTableSpacedChange}\r\n            onFocus={handleFocus}\r\n            disabled={!canUpdate}\r\n          />\r\n        </div>\r\n      </div>\r\n      <div className=\"row\">\r\n        <div className=\"col-12 col-md-3\">\r\n          <label htmlFor=\"plant-sticking-cuttings-per-hour\">\r\n            Sticking: Cuttings per Hour\r\n          </label>\r\n          <Input\r\n            id=\"plant-sticking-cuttings-per-hour\"\r\n            type=\"number\"\r\n            value={plant.stickingCuttingsPerHour}\r\n            onChange={handleStickingCuttingsPerHourChange}\r\n            onFocus={handleFocus}\r\n            disabled={!canUpdate}\r\n          />\r\n        </div>\r\n        <div className=\"col-12 col-md-3\">\r\n          <label htmlFor=\"plant-spacing-pots-per-hour\">\r\n            Spacing: Pots per Hour\r\n          </label>\r\n          <Input\r\n            id=\"plant-spacing-pots-per-hour\"\r\n            type=\"number\"\r\n            value={plant.spacingPotsPerHour}\r\n            onChange={handleSpacingPotsPerHourChange}\r\n            onFocus={handleFocus}\r\n            disabled={!canUpdate}\r\n          />\r\n        </div>\r\n        <div className=\"col-12 col-md-3\">\r\n          <label htmlFor=\"plant-packing-cases-per-hour\">\r\n            Packing: Cases per Hour\r\n          </label>\r\n          <Input\r\n            id=\"plant-packing-cases-per-hour\"\r\n            type=\"number\"\r\n            value={plant.packingCasesPerHour}\r\n            onChange={handlePackingCasesPerHourChange}\r\n            onFocus={handleFocus}\r\n            disabled={!canUpdate}\r\n          />\r\n        </div>\r\n        {!!plant.hasPinching && (\r\n          <div className=\"col-12 col-md-3\">\r\n            <label htmlFor=\"plant-pinching-pots-per-hour\">\r\n              Pinching: Pots per Hour\r\n            </label>\r\n            <Input\r\n              id=\"plant-pinching-pots-per-hour\"\r\n              type=\"number\"\r\n              value={plant.pinchingPotsPerHour}\r\n              onChange={handlePinchingingPotsPerHourChange}\r\n              onFocus={handleFocus}\r\n              disabled={!canUpdate}\r\n            />\r\n          </div>\r\n        )}\r\n      </div>\r\n      <div className=\"row my-3\">\r\n        <div className=\"col-12 col-md-3\">\r\n          <div className=\"form-check\">\r\n            <Input\r\n              id=\"plant-has-lights-out\"\r\n              type=\"checkbox\"\r\n              checked={plant.hasLightsOut}\r\n              onChange={handleHasLightsOutChange}\r\n              disabled={!canUpdate}\r\n            />\r\n            <label htmlFor=\"plant-has-lights-out\">Has Lights Out</label>\r\n          </div>\r\n        </div>\r\n        <div className=\"col-12 col-md-3\">\r\n          <div className=\"form-check\">\r\n            <Input\r\n              id=\"plant-needs-pinching\"\r\n              type=\"checkbox\"\r\n              checked={plant.hasPinching}\r\n              onChange={handleHasPinchingChange}\r\n              disabled={!canUpdate}\r\n            />\r\n            <label htmlFor=\"plant-needs-pinching\">Needs Pinching</label>\r\n          </div>\r\n          {!!plant.hasPinching && (\r\n            <FormGroup>\r\n              <label htmlFor=\"plant-pinching-pots-per-hour\">\r\n                Days after sticking to pinch\r\n              </label>\r\n              <Input\r\n                id=\"plant-pinching-pots-per-hour\"\r\n                type=\"number\"\r\n                value={plant.daysToPinch || ''}\r\n                onChange={handleDaysToPinchChange}\r\n                onFocus={handleFocus}\r\n                disabled={!canUpdate}\r\n              />\r\n            </FormGroup>\r\n          )}\r\n        </div>\r\n        <div className=\"col-12 col-md-3\">\r\n          <label htmlFor=\"plant-colour\">Colour</label>\r\n          {!!plant.colour && (\r\n            <InputGroup>\r\n              <Input\r\n                id=\"plant-colour\"\r\n                type=\"color\"\r\n                value={plant.colour || ''}\r\n                onChange={handleColourChange}\r\n                disabled={!canUpdate}\r\n              />\r\n              {canUpdate && (\r\n                <Button\r\n                  size=\"sm\"\r\n                  color=\"danger\"\r\n                  outline\r\n                  onClick={handleClearColourClick}>\r\n                  <FontAwesomeIcon icon={['fat', 'trash']} />\r\n                </Button>\r\n              )}\r\n            </InputGroup>\r\n          )}\r\n          {!plant.colour && (\r\n            <InputGroup>\r\n              <InputGroupText>No Colour</InputGroupText>\r\n              {canUpdate && (\r\n                <>\r\n                  <Button\r\n                    id=\"add-colour\"\r\n                    size=\"sm\"\r\n                    color=\"success\"\r\n                    outline\r\n                    onClick={handleAddColourClick}>\r\n                    <FontAwesomeIcon icon={['fat', 'palette']} />\r\n                  </Button>\r\n                  <UncontrolledTooltip target=\"add-colour\">\r\n                    Add Colour\r\n                  </UncontrolledTooltip>\r\n                </>\r\n              )}\r\n            </InputGroup>\r\n          )}\r\n        </div>\r\n      </div>\r\n      <div className=\"row\">\r\n        <div className=\"col-12 col-md-3\">\r\n          <div className=\"form-check\">\r\n            <Input\r\n              id=\"plant-has-varieties\"\r\n              type=\"checkbox\"\r\n              checked={hasVarieties}\r\n              onChange={handleHasVarietiesChange}\r\n              disabled={!canUpdate}\r\n            />\r\n            <label htmlFor=\"plant-has-varieties\">Has Varieties</label>\r\n          </div>\r\n        </div>\r\n        {hasVarieties && (\r\n          <div className=\"col-6\">\r\n            {canUpdate && (\r\n              <div className=\"row\">\r\n                <div className=\"col-5\">\r\n                  <FormGroup floating>\r\n                    <Input\r\n                      id=\"new-variety-name\"\r\n                      value={newVariety}\r\n                      onChange={handleNewVarietyChange}\r\n                      onKeyUp={handleNewVarietyKeyUp}\r\n                      placeholder=\"Add Variety\"\r\n                    />\r\n                    <Label htmlFor=\"new-variety-name\">Add Variety</Label>\r\n                  </FormGroup>\r\n                </div>\r\n                <div className=\"col-4\">\r\n                  <Button\r\n                    outline\r\n                    color=\"success\"\r\n                    onClick={handleAddNewVarietyClick}\r\n                    disabled={!newVariety}\r\n                    size=\"sm\">\r\n                    <FontAwesomeIcon icon={['fat', 'plus']} />\r\n                  </Button>\r\n                </div>\r\n              </div>\r\n            )}\r\n            {plant.varieties?.map((variety) => (\r\n              <div className=\"row\">\r\n                <div className=\"col-5\">\r\n                  <Input\r\n                    value={variety.name}\r\n                    onChange={(e) => handleVarietyNameChange(e, variety)}\r\n                    disabled={!canUpdate}\r\n                  />\r\n                </div>\r\n                <div className=\"col-3\">\r\n                  <Dropdown\r\n                    isOpen={varietyDropdownOpen[variety.name] || false}\r\n                    toggle={() => toggleVarietyDropdown(variety.name)}\r\n                    disabled={!canUpdate}>\r\n                    <DropdownToggle\r\n                      caret\r\n                      className=\"w-100 text-start d-flex align-items-center justify-content-between\"\r\n                      style={{\r\n                        backgroundColor: 'white',\r\n                        borderColor: '#ced4da',\r\n                        color: '#495057'\r\n                      }}>\r\n                      <div className=\"d-flex align-items-center\">\r\n                        {variety.colour ? (\r\n                          <>\r\n                            <div\r\n                              className='w16 h16 border rounded'\r\n                            />\r\n                            {variety.colour.name}\r\n                          </>\r\n                        ) : (\r\n                          '---'\r\n                        )}\r\n                      </div>\r\n                    </DropdownToggle>\r\n                    <DropdownMenu className=\"w-100\">\r\n                      <DropdownItem onClick={() => handleVarietyColourSelect(variety, '')}>\r\n                        ---\r\n                      </DropdownItem>\r\n                      {colours.map((c) => (\r\n                        <DropdownItem\r\n                          key={c.name}\r\n                          onClick={() => handleVarietyColourSelect(variety, c.name)}\r\n                          className=\"d-flex align-items-center\">\r\n                          <div\r\n                            style={{\r\n                              width: '16px',\r\n                              height: '16px',\r\n                              backgroundColor: c.hex,\r\n                              border: '1px solid #ccc',\r\n                              marginRight: '8px',\r\n                              borderRadius: '2px'\r\n                            }}\r\n                          />\r\n                          {c.name}\r\n                        </DropdownItem>\r\n                      ))}\r\n                    </DropdownMenu>\r\n                  </Dropdown>\r\n                </div>\r\n                {canUpdate && (\r\n                  <div className=\"col-4\">\r\n                    <Button\r\n                      outline\r\n                      color=\"danger\"\r\n                      onClick={() => handleDeleteVarietyClick(variety)}\r\n                      size=\"sm\">\r\n                      <FontAwesomeIcon icon={['fat', 'trash-alt']} />\r\n                    </Button>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            ))}\r\n          </div>\r\n        )}\r\n      </div>\r\n      <div className=\"row sticky-bottom bg-white border-top py-2\">\r\n        {!isNew && canDelete && (\r\n          <div className=\"col-auto\">\r\n            <Button\r\n              onClick={handleDeleteClick}\r\n              outline\r\n              color=\"danger\"\r\n              size=\"lg\"\r\n              className=\"me-auto\">\r\n              <FontAwesomeIcon icon={['fat', 'trash-alt']} />\r\n              &nbsp; Delete\r\n            </Button>\r\n          </div>\r\n        )}\r\n        <div className=\"col text-end\">\r\n          <Button tag={Link} to={routes.plants.path} outline size=\"lg\">\r\n            {canUpdate ? 'Cancel' : 'Close'}\r\n          </Button>\r\n          {canUpdate && (\r\n            <>\r\n              &nbsp;\r\n              <Button onClick={handleSaveClick} color=\"success\" size=\"lg\">\r\n                <FontAwesomeIcon icon={['fat', 'save']} />\r\n                &nbsp; Save\r\n              </Button>\r\n            </>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Detail;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAgB,OAAO;AAC1D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,SAAS,EAAEC,WAAW,QAAQ,cAAc;AACrD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SACEC,MAAM,EACNC,SAAS,EACTC,KAAK,EACLC,UAAU,EACVC,cAAc,EACdC,KAAK,EACLC,mBAAmB,EACnBC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,YAAY,QACP,YAAY;AACnB,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,MAAM,QAAQ,YAAY;AACnC,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,YAAY,QAA0E,gBAAgB;AAC/G,SAASC,WAAW,EAAEC,SAAS,EAAEC,WAAW,EAAEC,QAAQ,QAAQ,gBAAgB;AAC9E,SAASC,WAAW,QAAiB,mBAAmB;AACxD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,aAAa,QAAQ,gCAAgC;AAAC;AAAA;AAI/D,OAAO,SAASC,MAAM,GAAG;EAAA;EAAA;EACvB,MAAMC,QAAQ,GAAG3B,WAAW,EAAE;IAC5B4B,QAAQ,GAAG1B,WAAW,EAAE;IACxB;MAAE2B;IAAS,CAAC,GAAGZ,OAAO,EAAE;IACxB;MAAEa;IAAG,CAAC,GAAG7B,SAAS,EAAkB;IACpC8B,MAAM,GAAGhC,WAAW,CAACmB,YAAY,CAAC;IAClCc,KAAK,GAAGjC,WAAW,CAACsB,WAAW,CAAC;IAChCY,OAAO,GAAGlC,WAAW,CAAC0B,aAAa,CAAC;IACpC,CAACS,YAAY,EAAEC,eAAe,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;IACjD,CAACsC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;IAC1C,CAACwC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGzC,QAAQ,CAA2B,CAAC,CAAC,CAAC;IACtF0C,KAAK,GAAG,CAACR,KAAK,CAACS,IAAI;IACnBC,SAAS,GACNF,KAAK,IAAIX,QAAQ,CAAC,eAAe,CAAC,IAAKA,QAAQ,CAAC,eAAe,CAAC;IACnEc,SAAS,GAAGd,QAAQ,CAAC,eAAe,CAAC;EAEvChC,SAAS,CAAC,MAAM;IACd,MAAM+C,KAAK,GAAGb,MAAM,CAACc,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,GAAG,KAAKjB,EAAE,CAAC;IAC9C,IAAIc,KAAK,IAAIA,KAAK,CAACG,GAAG,KAAKf,KAAK,CAACe,GAAG,EAAE;MACpCpB,QAAQ,CAACL,QAAQ,CAACsB,KAAK,CAAC,CAAC;MACzBT,eAAe,CAAC,CAAC,CAACS,KAAK,CAACI,SAAS,CAAC;MAClCX,aAAa,CAAC,EAAE,CAAC;IACnB,CAAC,MAAM,IAAIP,EAAE,KAAK,KAAK,IAAIE,KAAK,CAACS,IAAI,EAAE;MACrCd,QAAQ,CAACL,QAAQ,CAACC,WAAW,EAAE,CAAC,CAAC;MACjCY,eAAe,CAAC,KAAK,CAAC;MACtBE,aAAa,CAAC,EAAE,CAAC;IACnB;EACF,CAAC,EAAE,CAACV,QAAQ,EAAEG,EAAE,EAAEE,KAAK,EAAED,MAAM,CAAC,CAAC;EAEjClC,SAAS,CAAC,MAAM;IACd,OAAO,SAASoD,OAAO,GAAG;MACxBtB,QAAQ,CAACL,QAAQ,CAACC,WAAW,EAAE,CAAC,CAAC;IACnC,CAAC;EACH,CAAC,EAAE,CAACI,QAAQ,CAAC,CAAC;EAEd,MAAMuB,gBAAgB,GAAIC,CAAsC,IAAK;IACnE,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK;MACzBC,IAAI,GAAI,GAAEH,IAAK,IAAGpB,KAAK,CAACwB,IAAK,EAAC;MAC9BC,MAAM,GAAG;QAAE,GAAGzB,KAAK;QAAEoB,IAAI;QAAEG;MAAK,CAAC;IAEnC5B,QAAQ,CAACL,QAAQ,CAACmC,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAMC,gBAAgB,GAAIP,CAAsC,IAAK;IACnE,MAAMK,IAAI,GAAGL,CAAC,CAACE,MAAM,CAACC,KAAK;MACzBC,IAAI,GAAI,GAAEvB,KAAK,CAACoB,IAAK,IAAGI,IAAK,EAAC;MAC9BC,MAAM,GAAG;QAAE,GAAGzB,KAAK;QAAEwB,IAAI;QAAED;MAAK,CAAC;IAEnC5B,QAAQ,CAACL,QAAQ,CAACmC,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAME,wBAAwB,GAAIR,CAAsC,IAAK;IAC3E,MAAMS,YAAY,GAAGT,CAAC,CAACE,MAAM,CAACC,KAAK;MACjCG,MAAM,GAAG;QAAE,GAAGzB,KAAK;QAAE4B;MAAa,CAAC;IAErCjC,QAAQ,CAACL,QAAQ,CAACmC,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAMI,0BAA0B,GAC9BV,CAAsC,IACnC;IACH,MAAMW,cAAc,GAAGX,CAAC,CAACE,MAAM,CAACU,aAAa;MAC3CN,MAAM,GAAG;QAAE,GAAGzB,KAAK;QAAE8B;MAAe,CAAC;IAEvCnC,QAAQ,CAACL,QAAQ,CAACmC,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAMO,mCAAmC,GACvCb,CAAsC,IACnC;IACH,MAAMc,uBAAuB,GAAGd,CAAC,CAACE,MAAM,CAACU,aAAa;MACpDN,MAAM,GAAG;QAAE,GAAGzB,KAAK;QAAEiC;MAAwB,CAAC;IAEhDtC,QAAQ,CAACL,QAAQ,CAACmC,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAMS,iCAAiC,GACrCf,CAAsC,IACnC;IACH,MAAMgB,qBAAqB,GAAGhB,CAAC,CAACE,MAAM,CAACU,aAAa;MAClDN,MAAM,GAAG;QAAE,GAAGzB,KAAK;QAAEmC;MAAsB,CAAC;IAE9CxC,QAAQ,CAACL,QAAQ,CAACmC,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAMW,kCAAkC,GACtCjB,CAAsC,IACnC;IACH,MAAMkB,sBAAsB,GAAGlB,CAAC,CAACE,MAAM,CAACU,aAAa;MACnDN,MAAM,GAAG;QAAE,GAAGzB,KAAK;QAAEqC;MAAuB,CAAC;IAE/C1C,QAAQ,CAACL,QAAQ,CAACmC,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAMa,2CAA2C,GAC/CnB,CAAsC,IACnC;IACH,MAAMoB,+BAA+B,GAAGpB,CAAC,CAACE,MAAM,CAACU,aAAa;MAC5DN,MAAM,GAAG;QAAE,GAAGzB,KAAK;QAAEuC;MAAgC,CAAC;IAExD5C,QAAQ,CAACL,QAAQ,CAACmC,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAMe,uBAAuB,GAAIrB,CAAsC,IAAK;IAC1E,MAAMsB,WAAW,GAAGtB,CAAC,CAACE,MAAM,CAACU,aAAa;MACxCN,MAAM,GAAG;QAAE,GAAGzB,KAAK;QAAEyC;MAAY,CAAC;IAEpC9C,QAAQ,CAACL,QAAQ,CAACmC,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAMiB,wBAAwB,GAAIvB,CAAsC,IAAK;IAC3E,MAAMwB,YAAY,GAAGxB,CAAC,CAACE,MAAM,CAACuB,OAAO;MACnCnB,MAAM,GAAG;QAAE,GAAGzB,KAAK;QAAE2C;MAAa,CAAC;IAErChD,QAAQ,CAACL,QAAQ,CAACmC,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAMoB,uBAAuB,GAAI1B,CAAsC,IAAK;IAC1E,MAAM2B,WAAW,GAAG3B,CAAC,CAACE,MAAM,CAACuB,OAAO;MAClCnB,MAAM,GAAG;QAAE,GAAGzB,KAAK;QAAE8C;MAAY,CAAC;IAEpC,IAAI,CAACA,WAAW,EAAE;MAChBrB,MAAM,CAACsB,mBAAmB,GAAG,CAAC;MAC9B,OAAOtB,MAAM,CAACuB,WAAW;IAC3B,CAAC,MAAM;MACL,IAAI,CAACvB,MAAM,CAACuB,WAAW,EAAE;QACvBvB,MAAM,CAACuB,WAAW,GAAG,CAAC;MACxB;IACF;IAEArD,QAAQ,CAACL,QAAQ,CAACmC,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAMwB,uBAAuB,GAAI9B,CAAsC,IAAK;IAC1E,MAAM6B,WAAW,GAAG7B,CAAC,CAACE,MAAM,CAACU,aAAa,IAAI,CAAC;MAC7CN,MAAM,GAAG;QAAE,GAAGzB,KAAK;QAAEgD;MAAY,CAAC;IAEpCrD,QAAQ,CAACL,QAAQ,CAACmC,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAMyB,kCAAkC,GACtC/B,CAAsC,IACnC;IACH,MAAM4B,mBAAmB,GAAG5B,CAAC,CAACE,MAAM,CAACU,aAAa;MAChDN,MAAM,GAAG;QAAE,GAAGzB,KAAK;QAAE+C;MAAoB,CAAC;IAE5CpD,QAAQ,CAACL,QAAQ,CAACmC,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAM0B,kBAAkB,GAAIhC,CAAsC,IAAK;IACrE,MAAMiC,MAAM,GAAGjC,CAAC,CAACE,MAAM,CAACC,KAAK;MAC3BG,MAAM,GAAG;QAAE,GAAGzB;MAAM,CAAC;IAEvB,IAAIoD,MAAM,EAAE;MACV3B,MAAM,CAAC2B,MAAM,GAAGA,MAAM;IACxB,CAAC,MAAM;MACL,OAAOpD,KAAK,CAACoD,MAAM;IACrB;IAEAzD,QAAQ,CAACL,QAAQ,CAACmC,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAM4B,sBAAsB,GAAG,MAAM;IACnC,MAAM5B,MAAM,GAAG;MAAE,GAAGzB;IAAM,CAAC;IAC3B,OAAOyB,MAAM,CAAC2B,MAAM;IACpBzD,QAAQ,CAACL,QAAQ,CAACmC,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAM6B,oBAAoB,GAAG,MAAM;IACjC,MAAM7B,MAAM,GAAG;MAAE,GAAGzB,KAAK;MAAEoD,MAAM,EAAE;IAAU,CAAC;IAC9CzD,QAAQ,CAACL,QAAQ,CAACmC,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAM8B,wBAAwB,GAAIpC,CAAsC,IAAK;IAC3E,MAAMjB,YAAY,GAAGiB,CAAC,CAACE,MAAM,CAACuB,OAAO;MACnCnB,MAAM,GAAG;QAAE,GAAGzB;MAAM,CAAC;IAEvB,IAAIE,YAAY,EAAE;MAChBuB,MAAM,CAACT,SAAS,GAAG,EAAE;IACvB,CAAC,MAAM;MACL,OAAOS,MAAM,CAACT,SAAS;IACzB;IAEArB,QAAQ,CAACL,QAAQ,CAACmC,MAAM,CAAC,CAAC;IAE1BtB,eAAe,CAACD,YAAY,CAAC;EAC/B,CAAC;EAED,MAAMsD,mCAAmC,GACvCrC,CAAsC,IACnC;IACH,MAAMsC,uBAAuB,GAAGtC,CAAC,CAACE,MAAM,CAACU,aAAa;MACpDN,MAAM,GAAG;QAAE,GAAGzB,KAAK;QAAEyD;MAAwB,CAAC;IAEhD9D,QAAQ,CAACL,QAAQ,CAACmC,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAMiC,8BAA8B,GAClCvC,CAAsC,IACnC;IACH,MAAMwC,kBAAkB,GAAGxC,CAAC,CAACE,MAAM,CAACU,aAAa;MAC/CN,MAAM,GAAG;QAAE,GAAGzB,KAAK;QAAE2D;MAAmB,CAAC;IAE3ChE,QAAQ,CAACL,QAAQ,CAACmC,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAMmC,+BAA+B,GACnCzC,CAAsC,IACnC;IACH,MAAM0C,mBAAmB,GAAG1C,CAAC,CAACE,MAAM,CAACU,aAAa;MAChDN,MAAM,GAAG;QAAE,GAAGzB,KAAK;QAAE6D;MAAoB,CAAC;IAE5ClE,QAAQ,CAACL,QAAQ,CAACmC,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAMqC,sBAAsB,GAAI3C,CAAsC,IAAK;IACzE,MAAMf,UAAU,GAAGe,CAAC,CAACE,MAAM,CAACC,KAAK;IACjCjB,aAAa,CAACD,UAAU,CAAC;EAC3B,CAAC;EAED,MAAM2D,qBAAqB,GAAI5C,CAAsB,IAAK;IACxD,IAAIf,UAAU,IAAIe,CAAC,CAAC6C,GAAG,KAAK,OAAO,EAAE;MACnCC,wBAAwB,EAAE;IAC5B;EACF,CAAC;EAED,MAAMA,wBAAwB,GAAG,MAAM;IACrC,IAAI7D,UAAU,EAAE;MACd,MAAMqB,MAAM,GAAG;UAAE,GAAGzB;QAAM,CAAC;QACzBgB,SAAS,GAAG,CAACS,MAAM,CAACT,SAAS,IAAI,EAAE,EAAEkD,GAAG,CAAEC,CAAC,KAAM;UAAE,GAAGA;QAAE,CAAC,CAAC,CAAC;MAE7DnD,SAAS,CAACoD,IAAI,CAAC;QAAE7C,IAAI,EAAEnB,UAAU;QAAEiE,iBAAiB,EAAE;MAAK,CAAC,CAAC;MAE7D5C,MAAM,CAACT,SAAS,GAAGA,SAAS;MAC5BrB,QAAQ,CAACL,QAAQ,CAACmC,MAAM,CAAC,CAAC;MAC1BpB,aAAa,CAAC,EAAE,CAAC;IACnB;EACF,CAAC;EAED,MAAMiE,uBAAuB,GAAG,CAC9BnD,CAAsC,EACtCoD,OAAgB,KACb;IACH,MAAM9C,MAAM,GAAG;QAAE,GAAGzB;MAAM,CAAC;MACzBgB,SAAS,GAAG,CAACS,MAAM,CAACT,SAAS,IAAI,EAAE,EAAEkD,GAAG,CAAEC,CAAC,KAAM;QAAE,GAAGA;MAAE,CAAC,CAAC,CAAC;MAC3DK,KAAK,GAAGxD,SAAS,CAACyD,SAAS,CAAEN,CAAC,IAAKA,CAAC,CAAC5C,IAAI,KAAKgD,OAAO,CAAChD,IAAI,CAAC;IAE7D,IAAIiD,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,MAAMjD,IAAI,GAAGJ,CAAC,CAACE,MAAM,CAACC,KAAK;QACzBoD,OAAO,GAAG;UAAE,GAAG1D,SAAS,CAACwD,KAAK,CAAC;UAAEjD;QAAK,CAAC;MACzCP,SAAS,CAAC2D,MAAM,CAACH,KAAK,EAAE,CAAC,EAAEE,OAAO,CAAC;IACrC;IAEAjD,MAAM,CAACT,SAAS,GAAGA,SAAS;IAE5BrB,QAAQ,CAACL,QAAQ,CAACmC,MAAM,CAAC,CAAC;EAC5B,CAAC;EAID,MAAMmD,wBAAwB,GAAIL,OAAgB,IAAK;IACrD,MAAM9C,MAAM,GAAG;QAAE,GAAGzB;MAAM,CAAC;MACzBgB,SAAS,GAAG,CAACS,MAAM,CAACT,SAAS,IAAI,EAAE,EAAEkD,GAAG,CAAEC,CAAC,KAAM;QAAE,GAAGA;MAAE,CAAC,CAAC,CAAC;MAC3DK,KAAK,GAAGxD,SAAS,CAACyD,SAAS,CAAEN,CAAC,IAAKA,CAAC,CAAC5C,IAAI,KAAKgD,OAAO,CAAChD,IAAI,CAAC;IAE7D,IAAIiD,KAAK,KAAK,CAAC,CAAC,EAAE;MAChBxD,SAAS,CAAC2D,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;IAC5B;IAEA/C,MAAM,CAACT,SAAS,GAAGA,SAAS;IAE5BrB,QAAQ,CAACL,QAAQ,CAACmC,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAMoD,qBAAqB,GAAIC,WAAmB,IAAK;IACrDvE,sBAAsB,CAACwE,IAAI,KAAK;MAC9B,GAAGA,IAAI;MACP,CAACD,WAAW,GAAG,CAACC,IAAI,CAACD,WAAW;IAClC,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,yBAAyB,GAAG,CAACT,OAAgB,EAAEU,UAAkB,KAAK;IAC1E,MAAM7B,MAAM,GAAG6B,UAAU,GAAGhF,OAAO,CAACY,IAAI,CAAEqE,CAAC,IAAKA,CAAC,CAAC3D,IAAI,KAAK0D,UAAU,CAAC,IAAI,IAAI,GAAG,IAAI;IACrF,MAAMxD,MAAM,GAAG;QAAE,GAAGzB;MAAM,CAAC;MACzBgB,SAAS,GAAG,CAACS,MAAM,CAACT,SAAS,IAAI,EAAE,EAAEkD,GAAG,CAAEC,CAAC,KAAM;QAAE,GAAGA;MAAE,CAAC,CAAC,CAAC;MAC3DK,KAAK,GAAGxD,SAAS,CAACyD,SAAS,CAAEN,CAAC,IAAKA,CAAC,CAAC5C,IAAI,KAAKgD,OAAO,CAAChD,IAAI,CAAC;IAE7D,IAAIiD,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,MAAME,OAAO,GAAG;QAAE,GAAG1D,SAAS,CAACwD,KAAK,CAAC;QAAEpB;MAAO,CAAC;MAC/CpC,SAAS,CAAC2D,MAAM,CAACH,KAAK,EAAE,CAAC,EAAEE,OAAO,CAAC;IACrC;IAEAjD,MAAM,CAACT,SAAS,GAAGA,SAAS;IAC5BrB,QAAQ,CAACL,QAAQ,CAACmC,MAAM,CAAC,CAAC;;IAE1B;IACAlB,sBAAsB,CAACwE,IAAI,KAAK;MAC9B,GAAGA,IAAI;MACP,CAACR,OAAO,CAAChD,IAAI,GAAG;IAClB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAM4D,eAAe,GAAG,YAAY;IAClC,MAAMC,MAAW,GAAG,MAAMzF,QAAQ,CAACP,SAAS,EAAE,CAAC;IAE/C,IAAI,CAACgG,MAAM,CAACC,KAAK,EAAE;MACjBzF,QAAQ,CAACZ,MAAM,CAACe,MAAM,CAACuF,IAAI,CAAC;IAC9B;EACF,CAAC;EAED,MAAMC,iBAAiB,GAAG,YAAY;IACpC,MAAMH,MAAW,GAAG,MAAMzF,QAAQ,CAACR,WAAW,EAAE,CAAC;IAEjD,IAAI,CAACiG,MAAM,CAACC,KAAK,EAAE;MACjBzF,QAAQ,CAACZ,MAAM,CAACe,MAAM,CAACuF,IAAI,CAAC;IAC9B;EACF,CAAC;EAED,oBACE;IAAK,SAAS,EAAC,wBAAwB;IAAA,wBACrC;MAAK,SAAS,EAAC,iDAAiD;MAAA,wBAC9D;QAAK,SAAS,EAAC,eAAe;QAAA,uBAC5B,QAAC,IAAI;UAAC,EAAE,EAAEtG,MAAM,CAACe,MAAM,CAACuF,IAAK;UAAA,wBAC3B,QAAC,eAAe;YAAC,IAAI,EAAE,CAAC,KAAK,EAAE,cAAc;UAAE;YAAA;YAAA;YAAA;UAAA,QAAG;QAAA;UAAA;UAAA;UAAA;QAAA;MAE7C;QAAA;QAAA;QAAA;MAAA,QACH,eACN;QAAI,SAAS,EAAC,KAAK;QAAA,UAAE9E,KAAK,GAAG,WAAW,GAAGR,KAAK,CAACuB;MAAI;QAAA;QAAA;QAAA;MAAA,QAAM;IAAA;MAAA;MAAA;MAAA;IAAA,QACvD,eACN;MAAK,SAAS,EAAC,KAAK;MAAA,wBAClB;QAAK,SAAS,EAAC,iBAAiB;QAAA,wBAC9B;UAAO,OAAO,EAAC,YAAY;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QAAa,eACxC,QAAC,KAAK;UACJ,EAAE,EAAC,YAAY;UACf,KAAK,EAAEvB,KAAK,CAACoB,IAAK;UAClB,QAAQ,EAAEF,gBAAiB;UAC3B,QAAQ,EAAE,CAACR;QAAU;UAAA;UAAA;UAAA;QAAA,QACrB;MAAA;QAAA;QAAA;QAAA;MAAA,QACE,eACN;QAAK,SAAS,EAAC,iBAAiB;QAAA,wBAC9B;UAAO,OAAO,EAAC,YAAY;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QAAa,eACxC,QAAC,KAAK;UACJ,EAAE,EAAC,YAAY;UACf,KAAK,EAAEV,KAAK,CAACwB,IAAK;UAClB,QAAQ,EAAEE,gBAAiB;UAC3B,QAAQ,EAAE,CAAChB;QAAU;UAAA;UAAA;UAAA;QAAA,QACrB;MAAA;QAAA;QAAA;QAAA;MAAA,QACE,eACN;QAAK,SAAS,EAAC,iBAAiB;QAAA,wBAC9B;UAAO,OAAO,EAAC,oBAAoB;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QAAqB,eACxD,QAAC,KAAK;UACJ,EAAE,EAAC,oBAAoB;UACvB,KAAK,EAAEV,KAAK,CAAC4B,YAAa;UAC1B,QAAQ,EAAED,wBAAyB;UACnC,QAAQ,EAAE,CAACjB;QAAU;UAAA;UAAA;UAAA;QAAA,QACrB;MAAA;QAAA;QAAA;QAAA;MAAA,QACE;IAAA;MAAA;MAAA;MAAA;IAAA,QACF,eACN;MAAK,SAAS,EAAC,KAAK;MAAA,wBAClB;QAAK,SAAS,EAAC,iBAAiB;QAAA,wBAC9B;UAAO,OAAO,EAAC,wBAAwB;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QAAyB,eAChE,QAAC,KAAK;UACJ,EAAE,EAAC,wBAAwB;UAC3B,IAAI,EAAC,QAAQ;UACb,KAAK,EAAEV,KAAK,CAAC8B,cAAe;UAC5B,QAAQ,EAAED,0BAA2B;UACrC,OAAO,EAAErC,WAAY;UACrB,QAAQ,EAAE,CAACkB;QAAU;UAAA;UAAA;UAAA;QAAA,QACrB;MAAA;QAAA;QAAA;QAAA;MAAA,QACE,eACN;QAAK,SAAS,EAAC,iBAAiB;QAAA,wBAC9B;UAAO,OAAO,EAAC,qBAAqB;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QAAsB,eAC1D,QAAC,KAAK;UACJ,EAAE,EAAC,qBAAqB;UACxB,IAAI,EAAC,QAAQ;UACb,KAAK,EAAEV,KAAK,CAACyC,WAAY;UACzB,QAAQ,EAAED,uBAAwB;UAClC,OAAO,EAAEhD,WAAY;UACrB,QAAQ,EAAE,CAACkB;QAAU;UAAA;UAAA;UAAA;QAAA,QACrB;MAAA;QAAA;QAAA;QAAA;MAAA,QACE,eACN;QAAK,SAAS,EAAC,iBAAiB;QAAA,wBAC9B;UAAO,OAAO,EAAC,kCAAkC;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QAEzC,eACR,QAAC,KAAK;UACJ,EAAE,EAAC,kCAAkC;UACrC,IAAI,EAAC,QAAQ;UACb,KAAK,EAAEV,KAAK,CAACiC,uBAAwB;UACrC,QAAQ,EAAED,mCAAoC;UAC9C,OAAO,EAAExC,WAAY;UACrB,QAAQ,EAAE,CAACkB;QAAU;UAAA;UAAA;UAAA;QAAA,QACrB;MAAA;QAAA;QAAA;QAAA;MAAA,QACE;IAAA;MAAA;MAAA;MAAA;IAAA,QACF,eACN;MAAK,SAAS,EAAC,KAAK;MAAA,wBAClB;QAAK,SAAS,EAAC,iBAAiB;QAAA,wBAC9B;UAAO,OAAO,EAAC,gCAAgC;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QAEvC,eACR,QAAC,KAAK;UACJ,EAAE,EAAC,gCAAgC;UACnC,IAAI,EAAC,QAAQ;UACb,KAAK,EAAEV,KAAK,CAACmC,qBAAsB;UACnC,QAAQ,EAAED,iCAAkC;UAC5C,OAAO,EAAE1C,WAAY;UACrB,QAAQ,EAAE,CAACkB;QAAU;UAAA;UAAA;UAAA;QAAA,QACrB;MAAA;QAAA;QAAA;QAAA;MAAA,QACE,eACN;QAAK,SAAS,EAAC,iBAAiB;QAAA,wBAC9B;UAAO,OAAO,EAAC,2CAA2C;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QAElD,eACR,QAAC,KAAK;UACJ,EAAE,EAAC,2CAA2C;UAC9C,IAAI,EAAC,QAAQ;UACb,KAAK,EAAEV,KAAK,CAACuC,+BAAgC;UAC7C,QAAQ,EAAED,2CAA4C;UACtD,OAAO,EAAE9C,WAAY;UACrB,QAAQ,EAAE,CAACkB;QAAU;UAAA;UAAA;UAAA;QAAA,QACrB;MAAA;QAAA;QAAA;QAAA;MAAA,QACE,eACN;QAAK,SAAS,EAAC,iBAAiB;QAAA,wBAC9B;UAAO,OAAO,EAAC,iCAAiC;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QAExC,eACR,QAAC,KAAK;UACJ,EAAE,EAAC,iCAAiC;UACpC,IAAI,EAAC,QAAQ;UACb,KAAK,EAAEV,KAAK,CAACqC,sBAAuB;UACpC,QAAQ,EAAED,kCAAmC;UAC7C,OAAO,EAAE5C,WAAY;UACrB,QAAQ,EAAE,CAACkB;QAAU;UAAA;UAAA;UAAA;QAAA,QACrB;MAAA;QAAA;QAAA;QAAA;MAAA,QACE;IAAA;MAAA;MAAA;MAAA;IAAA,QACF,eACN;MAAK,SAAS,EAAC,KAAK;MAAA,wBAClB;QAAK,SAAS,EAAC,iBAAiB;QAAA,wBAC9B;UAAO,OAAO,EAAC,kCAAkC;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QAEzC,eACR,QAAC,KAAK;UACJ,EAAE,EAAC,kCAAkC;UACrC,IAAI,EAAC,QAAQ;UACb,KAAK,EAAEV,KAAK,CAACyD,uBAAwB;UACrC,QAAQ,EAAED,mCAAoC;UAC9C,OAAO,EAAEhE,WAAY;UACrB,QAAQ,EAAE,CAACkB;QAAU;UAAA;UAAA;UAAA;QAAA,QACrB;MAAA;QAAA;QAAA;QAAA;MAAA,QACE,eACN;QAAK,SAAS,EAAC,iBAAiB;QAAA,wBAC9B;UAAO,OAAO,EAAC,6BAA6B;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QAEpC,eACR,QAAC,KAAK;UACJ,EAAE,EAAC,6BAA6B;UAChC,IAAI,EAAC,QAAQ;UACb,KAAK,EAAEV,KAAK,CAAC2D,kBAAmB;UAChC,QAAQ,EAAED,8BAA+B;UACzC,OAAO,EAAElE,WAAY;UACrB,QAAQ,EAAE,CAACkB;QAAU;UAAA;UAAA;UAAA;QAAA,QACrB;MAAA;QAAA;QAAA;QAAA;MAAA,QACE,eACN;QAAK,SAAS,EAAC,iBAAiB;QAAA,wBAC9B;UAAO,OAAO,EAAC,8BAA8B;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QAErC,eACR,QAAC,KAAK;UACJ,EAAE,EAAC,8BAA8B;UACjC,IAAI,EAAC,QAAQ;UACb,KAAK,EAAEV,KAAK,CAAC6D,mBAAoB;UACjC,QAAQ,EAAED,+BAAgC;UAC1C,OAAO,EAAEpE,WAAY;UACrB,QAAQ,EAAE,CAACkB;QAAU;UAAA;UAAA;UAAA;QAAA,QACrB;MAAA;QAAA;QAAA;QAAA;MAAA,QACE,EACL,CAAC,CAACV,KAAK,CAAC8C,WAAW,iBAClB;QAAK,SAAS,EAAC,iBAAiB;QAAA,wBAC9B;UAAO,OAAO,EAAC,8BAA8B;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QAErC,eACR,QAAC,KAAK;UACJ,EAAE,EAAC,8BAA8B;UACjC,IAAI,EAAC,QAAQ;UACb,KAAK,EAAE9C,KAAK,CAAC+C,mBAAoB;UACjC,QAAQ,EAAEG,kCAAmC;UAC7C,OAAO,EAAE1D,WAAY;UACrB,QAAQ,EAAE,CAACkB;QAAU;UAAA;UAAA;UAAA;QAAA,QACrB;MAAA;QAAA;QAAA;QAAA;MAAA,QAEL;IAAA;MAAA;MAAA;MAAA;IAAA,QACG,eACN;MAAK,SAAS,EAAC,UAAU;MAAA,wBACvB;QAAK,SAAS,EAAC,iBAAiB;QAAA,uBAC9B;UAAK,SAAS,EAAC,YAAY;UAAA,wBACzB,QAAC,KAAK;YACJ,EAAE,EAAC,sBAAsB;YACzB,IAAI,EAAC,UAAU;YACf,OAAO,EAAEV,KAAK,CAAC2C,YAAa;YAC5B,QAAQ,EAAED,wBAAyB;YACnC,QAAQ,EAAE,CAAChC;UAAU;YAAA;YAAA;YAAA;UAAA,QACrB,eACF;YAAO,OAAO,EAAC,sBAAsB;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAuB;QAAA;UAAA;UAAA;UAAA;QAAA;MACxD;QAAA;QAAA;QAAA;MAAA,QACF,eACN;QAAK,SAAS,EAAC,iBAAiB;QAAA,wBAC9B;UAAK,SAAS,EAAC,YAAY;UAAA,wBACzB,QAAC,KAAK;YACJ,EAAE,EAAC,sBAAsB;YACzB,IAAI,EAAC,UAAU;YACf,OAAO,EAAEV,KAAK,CAAC8C,WAAY;YAC3B,QAAQ,EAAED,uBAAwB;YAClC,QAAQ,EAAE,CAACnC;UAAU;YAAA;YAAA;YAAA;UAAA,QACrB,eACF;YAAO,OAAO,EAAC,sBAAsB;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAuB;QAAA;UAAA;UAAA;UAAA;QAAA,QACxD,EACL,CAAC,CAACV,KAAK,CAAC8C,WAAW,iBAClB,QAAC,SAAS;UAAA,wBACR;YAAO,OAAO,EAAC,8BAA8B;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAErC,eACR,QAAC,KAAK;YACJ,EAAE,EAAC,8BAA8B;YACjC,IAAI,EAAC,QAAQ;YACb,KAAK,EAAE9C,KAAK,CAACgD,WAAW,IAAI,EAAG;YAC/B,QAAQ,EAAEC,uBAAwB;YAClC,OAAO,EAAEzD,WAAY;YACrB,QAAQ,EAAE,CAACkB;UAAU;YAAA;YAAA;YAAA;UAAA,QACrB;QAAA;UAAA;UAAA;UAAA;QAAA,QAEL;MAAA;QAAA;QAAA;QAAA;MAAA,QACG,eACN;QAAK,SAAS,EAAC,iBAAiB;QAAA,wBAC9B;UAAO,OAAO,EAAC,cAAc;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QAAe,EAC3C,CAAC,CAACV,KAAK,CAACoD,MAAM,iBACb,QAAC,UAAU;UAAA,wBACT,QAAC,KAAK;YACJ,EAAE,EAAC,cAAc;YACjB,IAAI,EAAC,OAAO;YACZ,KAAK,EAAEpD,KAAK,CAACoD,MAAM,IAAI,EAAG;YAC1B,QAAQ,EAAED,kBAAmB;YAC7B,QAAQ,EAAE,CAACzC;UAAU;YAAA;YAAA;YAAA;UAAA,QACrB,EACDA,SAAS,iBACR,QAAC,MAAM;YACL,IAAI,EAAC,IAAI;YACT,KAAK,EAAC,QAAQ;YACd,OAAO;YACP,OAAO,EAAE2C,sBAAuB;YAAA,uBAChC,QAAC,eAAe;cAAC,IAAI,EAAE,CAAC,KAAK,EAAE,OAAO;YAAE;cAAA;cAAA;cAAA;YAAA;UAAG;YAAA;YAAA;YAAA;UAAA,QAE9C;QAAA;UAAA;UAAA;UAAA;QAAA,QAEJ,EACA,CAACrD,KAAK,CAACoD,MAAM,iBACZ,QAAC,UAAU;UAAA,wBACT,QAAC,cAAc;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAA2B,EACzC1C,SAAS,iBACR;YAAA,wBACE,QAAC,MAAM;cACL,EAAE,EAAC,YAAY;cACf,IAAI,EAAC,IAAI;cACT,KAAK,EAAC,SAAS;cACf,OAAO;cACP,OAAO,EAAE4C,oBAAqB;cAAA,uBAC9B,QAAC,eAAe;gBAAC,IAAI,EAAE,CAAC,KAAK,EAAE,SAAS;cAAE;gBAAA;gBAAA;gBAAA;cAAA;YAAG;cAAA;cAAA;cAAA;YAAA,QACtC,eACT,QAAC,mBAAmB;cAAC,MAAM,EAAC,YAAY;cAAA;YAAA;cAAA;cAAA;cAAA;YAAA,QAElB;UAAA,gBAEzB;QAAA;UAAA;UAAA;UAAA;QAAA,QAEJ;MAAA;QAAA;QAAA;QAAA;MAAA,QACG;IAAA;MAAA;MAAA;MAAA;IAAA,QACF,eACN;MAAK,SAAS,EAAC,KAAK;MAAA,wBAClB;QAAK,SAAS,EAAC,iBAAiB;QAAA,uBAC9B;UAAK,SAAS,EAAC,YAAY;UAAA,wBACzB,QAAC,KAAK;YACJ,EAAE,EAAC,qBAAqB;YACxB,IAAI,EAAC,UAAU;YACf,OAAO,EAAEpD,YAAa;YACtB,QAAQ,EAAEqD,wBAAyB;YACnC,QAAQ,EAAE,CAAC7C;UAAU;YAAA;YAAA;YAAA;UAAA,QACrB,eACF;YAAO,OAAO,EAAC,qBAAqB;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAsB;QAAA;UAAA;UAAA;UAAA;QAAA;MACtD;QAAA;QAAA;QAAA;MAAA,QACF,EACLR,YAAY,iBACX;QAAK,SAAS,EAAC,OAAO;QAAA,WACnBQ,SAAS,iBACR;UAAK,SAAS,EAAC,KAAK;UAAA,wBAClB;YAAK,SAAS,EAAC,OAAO;YAAA,uBACpB,QAAC,SAAS;cAAC,QAAQ;cAAA,wBACjB,QAAC,KAAK;gBACJ,EAAE,EAAC,kBAAkB;gBACrB,KAAK,EAAEN,UAAW;gBAClB,QAAQ,EAAE0D,sBAAuB;gBACjC,OAAO,EAAEC,qBAAsB;gBAC/B,WAAW,EAAC;cAAa;gBAAA;gBAAA;gBAAA;cAAA,QACzB,eACF,QAAC,KAAK;gBAAC,OAAO,EAAC,kBAAkB;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA,QAAoB;YAAA;cAAA;cAAA;cAAA;YAAA;UAC3C;YAAA;YAAA;YAAA;UAAA,QACR,eACN;YAAK,SAAS,EAAC,OAAO;YAAA,uBACpB,QAAC,MAAM;cACL,OAAO;cACP,KAAK,EAAC,SAAS;cACf,OAAO,EAAEE,wBAAyB;cAClC,QAAQ,EAAE,CAAC7D,UAAW;cACtB,IAAI,EAAC,IAAI;cAAA,uBACT,QAAC,eAAe;gBAAC,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM;cAAE;gBAAA;gBAAA;gBAAA;cAAA;YAAG;cAAA;cAAA;cAAA;YAAA;UACnC;YAAA;YAAA;YAAA;UAAA,QACL;QAAA;UAAA;UAAA;UAAA;QAAA,QAET,sBACAJ,KAAK,CAACgB,SAAS,qDAAf,iBAAiBkD,GAAG,CAAEK,OAAO,iBAC5B;UAAK,SAAS,EAAC,KAAK;UAAA,wBAClB;YAAK,SAAS,EAAC,OAAO;YAAA,uBACpB,QAAC,KAAK;cACJ,KAAK,EAAEA,OAAO,CAAChD,IAAK;cACpB,QAAQ,EAAGJ,CAAC,IAAKmD,uBAAuB,CAACnD,CAAC,EAAEoD,OAAO,CAAE;cACrD,QAAQ,EAAE,CAAC7D;YAAU;cAAA;cAAA;cAAA;YAAA;UACrB;YAAA;YAAA;YAAA;UAAA,QACE,eACN;YAAK,SAAS,EAAC,OAAO;YAAA,uBACpB,QAAC,QAAQ;cACP,MAAM,EAAEJ,mBAAmB,CAACiE,OAAO,CAAChD,IAAI,CAAC,IAAI,KAAM;cACnD,MAAM,EAAE,MAAMsD,qBAAqB,CAACN,OAAO,CAAChD,IAAI,CAAE;cAClD,QAAQ,EAAE,CAACb,SAAU;cAAA,wBACrB,QAAC,cAAc;gBACb,KAAK;gBACL,SAAS,EAAC,oEAAoE;gBAC9E,KAAK,EAAE;kBACL8E,eAAe,EAAE,OAAO;kBACxBC,WAAW,EAAE,SAAS;kBACtBC,KAAK,EAAE;gBACT,CAAE;gBAAA,uBACF;kBAAK,SAAS,EAAC,2BAA2B;kBAAA,UACvCnB,OAAO,CAACnB,MAAM,gBACb;oBAAA,wBACE;sBACE,SAAS,EAAC;oBAAwB;sBAAA;sBAAA;sBAAA;oBAAA,QAClC,EACDmB,OAAO,CAACnB,MAAM,CAAC7B,IAAI;kBAAA,gBACnB,GAEH;gBACD;kBAAA;kBAAA;kBAAA;gBAAA;cACG;gBAAA;gBAAA;gBAAA;cAAA,QACS,eACjB,QAAC,YAAY;gBAAC,SAAS,EAAC,OAAO;gBAAA,wBAC7B,QAAC,YAAY;kBAAC,OAAO,EAAE,MAAMyD,yBAAyB,CAACT,OAAO,EAAE,EAAE,CAAE;kBAAA;gBAAA;kBAAA;kBAAA;kBAAA;gBAAA,QAErD,EACdtE,OAAO,CAACiE,GAAG,CAAEgB,CAAC,iBACb,QAAC,YAAY;kBAEX,OAAO,EAAE,MAAMF,yBAAyB,CAACT,OAAO,EAAEW,CAAC,CAAC3D,IAAI,CAAE;kBAC1D,SAAS,EAAC,2BAA2B;kBAAA,wBACrC;oBACE,KAAK,EAAE;sBACLoE,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE,MAAM;sBACdJ,eAAe,EAAEN,CAAC,CAACW,GAAG;sBACtBC,MAAM,EAAE,gBAAgB;sBACxBC,WAAW,EAAE,KAAK;sBAClBC,YAAY,EAAE;oBAChB;kBAAE;oBAAA;oBAAA;oBAAA;kBAAA,QACF,EACDd,CAAC,CAAC3D,IAAI;gBAAA,GAbF2D,CAAC,CAAC3D,IAAI;kBAAA;kBAAA;kBAAA;gBAAA,QAed,CAAC;cAAA;gBAAA;gBAAA;gBAAA;cAAA,QACW;YAAA;cAAA;cAAA;cAAA;YAAA;UACN;YAAA;YAAA;YAAA;UAAA,QACP,EACLb,SAAS,iBACR;YAAK,SAAS,EAAC,OAAO;YAAA,uBACpB,QAAC,MAAM;cACL,OAAO;cACP,KAAK,EAAC,QAAQ;cACd,OAAO,EAAE,MAAMkE,wBAAwB,CAACL,OAAO,CAAE;cACjD,IAAI,EAAC,IAAI;cAAA,uBACT,QAAC,eAAe;gBAAC,IAAI,EAAE,CAAC,KAAK,EAAE,WAAW;cAAE;gBAAA;gBAAA;gBAAA;cAAA;YAAG;cAAA;cAAA;cAAA;YAAA;UACxC;YAAA;YAAA;YAAA;UAAA,QAEZ;QAAA;UAAA;UAAA;UAAA;QAAA,QAEJ,CAAC;MAAA;QAAA;QAAA;QAAA;MAAA,QAEL;IAAA;MAAA;MAAA;MAAA;IAAA,QACG,eACN;MAAK,SAAS,EAAC,4CAA4C;MAAA,WACxD,CAAC/D,KAAK,IAAIG,SAAS,iBAClB;QAAK,SAAS,EAAC,UAAU;QAAA,uBACvB,QAAC,MAAM;UACL,OAAO,EAAE4E,iBAAkB;UAC3B,OAAO;UACP,KAAK,EAAC,QAAQ;UACd,IAAI,EAAC,IAAI;UACT,SAAS,EAAC,SAAS;UAAA,wBACnB,QAAC,eAAe;YAAC,IAAI,EAAE,CAAC,KAAK,EAAE,WAAW;UAAE;YAAA;YAAA;YAAA;UAAA,QAAG;QAAA;UAAA;UAAA;UAAA;QAAA;MAExC;QAAA;QAAA;QAAA;MAAA,QAEZ,eACD;QAAK,SAAS,EAAC,cAAc;QAAA,wBAC3B,QAAC,MAAM;UAAC,GAAG,EAAEpH,IAAK;UAAC,EAAE,EAAEa,MAAM,CAACe,MAAM,CAACuF,IAAK;UAAC,OAAO;UAAC,IAAI,EAAC,IAAI;UAAA,UACzD5E,SAAS,GAAG,QAAQ,GAAG;QAAO;UAAA;UAAA;UAAA;QAAA,QACxB,EACRA,SAAS,iBACR;UAAA,gCAEE,QAAC,MAAM;YAAC,OAAO,EAAEyE,eAAgB;YAAC,KAAK,EAAC,SAAS;YAAC,IAAI,EAAC,IAAI;YAAA,wBACzD,QAAC,eAAe;cAAC,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM;YAAE;cAAA;cAAA;cAAA;YAAA,QAAG;UAAA;YAAA;YAAA;YAAA;UAAA,QAEnC;QAAA,gBAEZ;MAAA;QAAA;QAAA;QAAA;MAAA,QACG;IAAA;MAAA;MAAA;MAAA;IAAA,QACF;EAAA;IAAA;IAAA;IAAA;EAAA,QACF;AAEV;AAAC,GAptBezF,MAAM;EAAA,QACH1B,WAAW,EACfE,WAAW,EACPe,OAAO,EACbhB,SAAS,EACTF,WAAW,EACZA,WAAW,EACTA,WAAW;AAAA;AAAA,KAPT2B,MAAM;AAstBtB,eAAeA,MAAM;AAAC;AAAA"}, "metadata": {}, "sourceType": "module"}