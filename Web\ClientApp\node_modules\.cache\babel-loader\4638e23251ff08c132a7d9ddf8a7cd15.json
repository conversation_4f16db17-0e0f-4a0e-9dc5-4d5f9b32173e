{"ast": null, "code": "import { createAction, createAsyncThunk, createSlice } from '@reduxjs/toolkit';\nimport { createPlant } from 'api/models/plants';\nimport { plantApi } from 'api/plant-service';\nimport { generateAndSaveVarietySortOrder, moveVarietyAndSave } from './plants-slice';\nconst initialState = {\n  isLoading: false,\n  plant: createPlant(),\n  error: null\n};\nexport const savePlant = createAsyncThunk('plant-detail/save-plant', async (_, _ref) => {\n  let {\n    rejectWithValue,\n    getState\n  } = _ref;\n  try {\n    const plant = state.plantDetail.plant,\n      doc = {\n        ...plant\n      };\n    const updated = await plantApi.save(doc);\n    return updated;\n  } catch (e) {\n    return rejectWithValue(e);\n  }\n});\nexport const deletePlant = createAsyncThunk('plant-detail/delete-plant', async (_, _ref2) => {\n  let {\n    rejectWithValue,\n    getState\n  } = _ref2;\n  try {\n    const plant = getState().plantDetail.plant,\n      doc = {\n        ...plant\n      };\n    const updated = await plantApi.delete(doc);\n    return updated;\n  } catch (e) {\n    return rejectWithValue(e);\n  }\n});\nconst savePending = createAction(savePlant.pending.type),\n  saveFulfilled = createAction(savePlant.fulfilled.type),\n  saveRejected = createAction(savePlant.rejected.type),\n  deletePending = createAction(deletePlant.pending.type),\n  deleteFulfilled = createAction(deletePlant.fulfilled.type),\n  deleteRejected = createAction(deletePlant.rejected.type);\nexport const plantDetailSlice = createSlice({\n  name: 'plant-detail',\n  initialState,\n  reducers: {\n    setPlant(state, action) {\n      state.plant = action.payload;\n    }\n  },\n  extraReducers: builder => builder.addCase(savePending, state => {\n    state.isLoading = true;\n    state.error = null;\n  }).addCase(saveFulfilled, (state, action) => {\n    state.isLoading = false;\n    if (action.payload) {\n      state.plant = action.payload;\n    }\n  }).addCase(saveRejected, (state, action) => {\n    state.isLoading = false;\n    state.error = action.payload;\n  }).addCase(deletePending, state => {\n    state.isLoading = true;\n    state.error = null;\n  }).addCase(deleteFulfilled, (state, action) => {\n    state.isLoading = false;\n    state.plant = createPlant();\n  }).addCase(deleteRejected, (state, action) => {\n    state.isLoading = false;\n    state.error = action.payload;\n  }).addCase(generateAndSaveVarietySortOrder.fulfilled, (state, action) => {\n    // Update the local plant state when variety sort order is generated\n    if (action.payload._id === state.plant._id) {\n      state.plant = action.payload;\n    }\n  }).addCase(moveVarietyAndSave.fulfilled, (state, action) => {\n    // Update the local plant state when varieties are reordered\n    if (action.payload._id === state.plant._id) {\n      state.plant = action.payload;\n    }\n  })\n});\nexport const {\n  setPlant\n} = plantDetailSlice.actions;\nexport const selectPlant = state => state.plantDetail.plant;\nexport const selectIsLoading = state => state.plantDetail.isLoading;\nexport const selectError = state => state.plantDetail.error;\nexport default plantDetailSlice.reducer;", "map": {"version": 3, "names": ["createAction", "createAsyncThunk", "createSlice", "createPlant", "plantApi", "generateAndSaveVarietySortOrder", "moveVarietyAndSave", "initialState", "isLoading", "plant", "error", "savePlant", "_", "rejectWithValue", "getState", "state", "plantDetail", "doc", "updated", "save", "e", "deletePlant", "delete", "savePending", "pending", "type", "saveFulfilled", "fulfilled", "saveRejected", "rejected", "deletePending", "deleteFulfilled", "deleteRejected", "plantDetailSlice", "name", "reducers", "setPlant", "action", "payload", "extraReducers", "builder", "addCase", "_id", "actions", "selectPlant", "selectIsLoading", "selectError", "reducer"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/plants/detail-slice.ts"], "sourcesContent": ["import { AsyncThunk, createAction, createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';\r\nimport { createPlant, Plant } from 'api/models/plants';\r\nimport { plantApi } from 'api/plant-service';\r\nimport { RootState } from 'app/store';\r\nimport { ProblemDetails } from 'utils/problem-details';\r\nimport { generateAndSaveVarietySortOrder, moveVarietyAndSave } from './plants-slice';\r\n\r\ninterface PlantDetailState {\r\n  isLoading: boolean;\r\n  plant: Plant\r\n  error: ProblemDetails | null;\r\n}\r\n\r\nconst initialState: PlantDetailState = {\r\n  isLoading: false,\r\n  plant: createPlant(),\r\n  error: null\r\n};\r\n\r\nexport const savePlant: AsyncThunk<Plant, void, {state: RootState}> = createAsyncThunk(\r\n  'plant-detail/save-plant',\r\n  async (_, {rejectWithValue, getState}) => {\r\n    try {\r\n\r\n      const plant = state.plantDetail.plant,\r\n        doc = {...plant};\r\n      \r\n      const updated = await plantApi.save(doc);\r\n      return updated;\r\n\r\n    } catch(e) {\r\n      return rejectWithValue(e as ProblemDetails);\r\n    }\r\n  }\r\n);\r\n\r\nexport const deletePlant: AsyncThunk<void, void, {state: RootState}> = createAsyncThunk(\r\n  'plant-detail/delete-plant',\r\n  async (_, {rejectWithValue, getState}) => {\r\n    try {\r\n\r\n      const plant = (getState() as RootState).plantDetail.plant,\r\n        doc = {...plant};\r\n      \r\n      const updated = await plantApi.delete(doc);\r\n      return updated;\r\n\r\n    } catch(e) {\r\n      return rejectWithValue(e as ProblemDetails);\r\n    }\r\n  }\r\n);\r\n\r\nconst savePending = createAction(savePlant.pending.type),\r\n  saveFulfilled = createAction<Plant | undefined>(savePlant.fulfilled.type),\r\n  saveRejected = createAction<ProblemDetails>(savePlant.rejected.type),\r\n  deletePending = createAction(deletePlant.pending.type),\r\n  deleteFulfilled = createAction(deletePlant.fulfilled.type),\r\n  deleteRejected = createAction<ProblemDetails>(deletePlant.rejected.type);\r\n\r\nexport const plantDetailSlice = createSlice({\r\n  name: 'plant-detail',\r\n  initialState,\r\n  reducers: {\r\n    setPlant(state, action: PayloadAction<Plant>) {\r\n      state.plant = action.payload;\r\n    }\r\n  },\r\n  extraReducers: builder =>\r\n    builder\r\n      .addCase(savePending, state => {\r\n        state.isLoading = true;\r\n        state.error = null;\r\n      })\r\n      .addCase(saveFulfilled, (state, action) => {\r\n        state.isLoading = false;\r\n        if(action.payload) {\r\n          state.plant = action.payload;\r\n        }\r\n      })\r\n      .addCase(saveRejected, (state, action) => {\r\n        state.isLoading = false;\r\n        state.error = action.payload;\r\n      })\r\n      .addCase(deletePending, state => {\r\n        state.isLoading = true;\r\n        state.error = null;\r\n      })\r\n      .addCase(deleteFulfilled, (state, action) => {\r\n        state.isLoading = false;\r\n        state.plant = createPlant();\r\n      })\r\n      .addCase(deleteRejected, (state, action) => {\r\n        state.isLoading = false;\r\n        state.error = action.payload;\r\n      })\r\n      .addCase(generateAndSaveVarietySortOrder.fulfilled, (state, action) => {\r\n        // Update the local plant state when variety sort order is generated\r\n        if (action.payload._id === state.plant._id) {\r\n          state.plant = action.payload;\r\n        }\r\n      })\r\n      .addCase(moveVarietyAndSave.fulfilled, (state, action) => {\r\n        // Update the local plant state when varieties are reordered\r\n        if (action.payload._id === state.plant._id) {\r\n          state.plant = action.payload;\r\n        }\r\n      })\r\n});\r\n\r\nexport const { setPlant } = plantDetailSlice.actions;\r\n\r\nexport const selectPlant = (state: RootState) => state.plantDetail.plant;\r\nexport const selectIsLoading = (state: RootState) => state.plantDetail.isLoading;\r\nexport const selectError = (state: RootState) => state.plantDetail.error;\r\n\r\nexport default plantDetailSlice.reducer;\r\n"], "mappings": "AAAA,SAAqBA,YAAY,EAAEC,gBAAgB,EAAEC,WAAW,QAAuB,kBAAkB;AACzG,SAASC,WAAW,QAAe,mBAAmB;AACtD,SAASC,QAAQ,QAAQ,mBAAmB;AAG5C,SAASC,+BAA+B,EAAEC,kBAAkB,QAAQ,gBAAgB;AAQpF,MAAMC,YAA8B,GAAG;EACrCC,SAAS,EAAE,KAAK;EAChBC,KAAK,EAAEN,WAAW,EAAE;EACpBO,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,SAAsD,GAAGV,gBAAgB,CACpF,yBAAyB,EACzB,OAAOW,CAAC,WAAkC;EAAA,IAAhC;IAACC,eAAe;IAAEC;EAAQ,CAAC;EACnC,IAAI;IAEF,MAAML,KAAK,GAAGM,KAAK,CAACC,WAAW,CAACP,KAAK;MACnCQ,GAAG,GAAG;QAAC,GAAGR;MAAK,CAAC;IAElB,MAAMS,OAAO,GAAG,MAAMd,QAAQ,CAACe,IAAI,CAACF,GAAG,CAAC;IACxC,OAAOC,OAAO;EAEhB,CAAC,CAAC,OAAME,CAAC,EAAE;IACT,OAAOP,eAAe,CAACO,CAAC,CAAmB;EAC7C;AACF,CAAC,CACF;AAED,OAAO,MAAMC,WAAuD,GAAGpB,gBAAgB,CACrF,2BAA2B,EAC3B,OAAOW,CAAC,YAAkC;EAAA,IAAhC;IAACC,eAAe;IAAEC;EAAQ,CAAC;EACnC,IAAI;IAEF,MAAML,KAAK,GAAIK,QAAQ,EAAE,CAAeE,WAAW,CAACP,KAAK;MACvDQ,GAAG,GAAG;QAAC,GAAGR;MAAK,CAAC;IAElB,MAAMS,OAAO,GAAG,MAAMd,QAAQ,CAACkB,MAAM,CAACL,GAAG,CAAC;IAC1C,OAAOC,OAAO;EAEhB,CAAC,CAAC,OAAME,CAAC,EAAE;IACT,OAAOP,eAAe,CAACO,CAAC,CAAmB;EAC7C;AACF,CAAC,CACF;AAED,MAAMG,WAAW,GAAGvB,YAAY,CAACW,SAAS,CAACa,OAAO,CAACC,IAAI,CAAC;EACtDC,aAAa,GAAG1B,YAAY,CAAoBW,SAAS,CAACgB,SAAS,CAACF,IAAI,CAAC;EACzEG,YAAY,GAAG5B,YAAY,CAAiBW,SAAS,CAACkB,QAAQ,CAACJ,IAAI,CAAC;EACpEK,aAAa,GAAG9B,YAAY,CAACqB,WAAW,CAACG,OAAO,CAACC,IAAI,CAAC;EACtDM,eAAe,GAAG/B,YAAY,CAACqB,WAAW,CAACM,SAAS,CAACF,IAAI,CAAC;EAC1DO,cAAc,GAAGhC,YAAY,CAAiBqB,WAAW,CAACQ,QAAQ,CAACJ,IAAI,CAAC;AAE1E,OAAO,MAAMQ,gBAAgB,GAAG/B,WAAW,CAAC;EAC1CgC,IAAI,EAAE,cAAc;EACpB3B,YAAY;EACZ4B,QAAQ,EAAE;IACRC,QAAQ,CAACrB,KAAK,EAAEsB,MAA4B,EAAE;MAC5CtB,KAAK,CAACN,KAAK,GAAG4B,MAAM,CAACC,OAAO;IAC9B;EACF,CAAC;EACDC,aAAa,EAAEC,OAAO,IACpBA,OAAO,CACJC,OAAO,CAAClB,WAAW,EAAER,KAAK,IAAI;IAC7BA,KAAK,CAACP,SAAS,GAAG,IAAI;IACtBO,KAAK,CAACL,KAAK,GAAG,IAAI;EACpB,CAAC,CAAC,CACD+B,OAAO,CAACf,aAAa,EAAE,CAACX,KAAK,EAAEsB,MAAM,KAAK;IACzCtB,KAAK,CAACP,SAAS,GAAG,KAAK;IACvB,IAAG6B,MAAM,CAACC,OAAO,EAAE;MACjBvB,KAAK,CAACN,KAAK,GAAG4B,MAAM,CAACC,OAAO;IAC9B;EACF,CAAC,CAAC,CACDG,OAAO,CAACb,YAAY,EAAE,CAACb,KAAK,EAAEsB,MAAM,KAAK;IACxCtB,KAAK,CAACP,SAAS,GAAG,KAAK;IACvBO,KAAK,CAACL,KAAK,GAAG2B,MAAM,CAACC,OAAO;EAC9B,CAAC,CAAC,CACDG,OAAO,CAACX,aAAa,EAAEf,KAAK,IAAI;IAC/BA,KAAK,CAACP,SAAS,GAAG,IAAI;IACtBO,KAAK,CAACL,KAAK,GAAG,IAAI;EACpB,CAAC,CAAC,CACD+B,OAAO,CAACV,eAAe,EAAE,CAAChB,KAAK,EAAEsB,MAAM,KAAK;IAC3CtB,KAAK,CAACP,SAAS,GAAG,KAAK;IACvBO,KAAK,CAACN,KAAK,GAAGN,WAAW,EAAE;EAC7B,CAAC,CAAC,CACDsC,OAAO,CAACT,cAAc,EAAE,CAACjB,KAAK,EAAEsB,MAAM,KAAK;IAC1CtB,KAAK,CAACP,SAAS,GAAG,KAAK;IACvBO,KAAK,CAACL,KAAK,GAAG2B,MAAM,CAACC,OAAO;EAC9B,CAAC,CAAC,CACDG,OAAO,CAACpC,+BAA+B,CAACsB,SAAS,EAAE,CAACZ,KAAK,EAAEsB,MAAM,KAAK;IACrE;IACA,IAAIA,MAAM,CAACC,OAAO,CAACI,GAAG,KAAK3B,KAAK,CAACN,KAAK,CAACiC,GAAG,EAAE;MAC1C3B,KAAK,CAACN,KAAK,GAAG4B,MAAM,CAACC,OAAO;IAC9B;EACF,CAAC,CAAC,CACDG,OAAO,CAACnC,kBAAkB,CAACqB,SAAS,EAAE,CAACZ,KAAK,EAAEsB,MAAM,KAAK;IACxD;IACA,IAAIA,MAAM,CAACC,OAAO,CAACI,GAAG,KAAK3B,KAAK,CAACN,KAAK,CAACiC,GAAG,EAAE;MAC1C3B,KAAK,CAACN,KAAK,GAAG4B,MAAM,CAACC,OAAO;IAC9B;EACF,CAAC;AACP,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEF;AAAS,CAAC,GAAGH,gBAAgB,CAACU,OAAO;AAEpD,OAAO,MAAMC,WAAW,GAAI7B,KAAgB,IAAKA,KAAK,CAACC,WAAW,CAACP,KAAK;AACxE,OAAO,MAAMoC,eAAe,GAAI9B,KAAgB,IAAKA,KAAK,CAACC,WAAW,CAACR,SAAS;AAChF,OAAO,MAAMsC,WAAW,GAAI/B,KAAgB,IAAKA,KAAK,CAACC,WAAW,CAACN,KAAK;AAExE,eAAeuB,gBAAgB,CAACc,OAAO"}, "metadata": {}, "sourceType": "module"}