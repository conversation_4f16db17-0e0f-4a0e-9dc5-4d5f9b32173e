{"ast": null, "code": "import { guid } from 'utils/guid';\nexport const PlantType = 'plant';\nexport function createPlant(basePlant) {\n  const plant = {\n    _id: guid(),\n    type: PlantType,\n    name: '',\n    abbreviation: '',\n    crop: '',\n    size: '',\n    cuttingsPerPot: 1,\n    cuttingsPerTableTight: 1,\n    cuttingsPerTableSpaced: 1,\n    cuttingsPerTablePartiallySpaced: 1,\n    potsPerCase: 1,\n    hasLightsOut: true,\n    hasPinching: false,\n    pinchingPotsPerHour: 0,\n    stickingCuttingsPerHour: 1,\n    spacingPotsPerHour: 1,\n    packingCasesPerHour: 1,\n    defaultStickingCrewSize: 1\n  };\n  if (basePlant) {\n    Object.assign(plant, basePlant);\n  }\n  return plant;\n}", "map": {"version": 3, "names": ["guid", "PlantType", "createPlant", "basePlant", "plant", "_id", "type", "name", "abbreviation", "crop", "size", "cuttingsPerPot", "cuttingsPerTableTight", "cuttingsPerTableSpaced", "cuttingsPerTablePartiallySpaced", "potsPerCase", "hasLightsOut", "hasPinching", "pinchingPotsPerHour", "stickingCuttingsPerHour", "spacingPotsPerHour", "packingCasesPerHour", "defaultStickingCrewSize", "Object", "assign"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/api/models/plants.ts"], "sourcesContent": ["import { guid } from 'utils/guid';\r\nimport { ModelBase } from './model-base';\r\nimport { Colour } from './colour';\r\n\r\nexport const PlantType = 'plant';\r\n\r\nexport interface Plant extends ModelBase {\r\n  type: string;\r\n  name: string;\r\n  abbreviation: string;\r\n  crop: string;\r\n  size: string;\r\n  colour?: string | null;\r\n  cuttingsPerPot: number;\r\n  cuttingsPerTableTight: number;\r\n  cuttingsPerTableSpaced: number;\r\n  cuttingsPerTablePartiallySpaced: number;\r\n  potsPerCase: number;\r\n  hasLightsOut: boolean;\r\n  hasPinching: boolean;\r\n  pinchingPotsPerHour: number;\r\n  daysToPinch?: number;\r\n  stickingCuttingsPerHour: number;\r\n  spacingPotsPerHour: number;\r\n  packingCasesPerHour: number;\r\n  defaultStickingCrewSize: number;\r\n  varieties?: Variety[];\r\n}\r\n\r\nexport interface Variety {\r\n  name: string;\r\n  colour?: Colour | null;\r\n  stickingSortOrder?: number | null;\r\n}\r\n\r\nexport function createPlant(basePlant?: Plant) {\r\n  const plant: Plant = {\r\n    _id: guid(),\r\n    type: PlantType,\r\n    name: '',\r\n    abbreviation: '',\r\n    crop: '',\r\n    size: '',\r\n    cuttingsPerPot: 1,\r\n    cuttingsPerTableTight: 1,\r\n    cuttingsPerTableSpaced: 1,\r\n    cuttingsPerTablePartiallySpaced: 1,\r\n    potsPerCase: 1,\r\n    hasLightsOut: true,\r\n    hasPinching: false,\r\n    pinchingPotsPerHour: 0,\r\n    stickingCuttingsPerHour: 1,\r\n    spacingPotsPerHour: 1,\r\n    packingCasesPerHour: 1,\r\n    defaultStickingCrewSize: 1,\r\n  }\r\n\r\n  if(basePlant) {\r\n    Object.assign(plant, basePlant);\r\n  }\r\n\r\n  return plant;\r\n}\r\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,YAAY;AAIjC,OAAO,MAAMC,SAAS,GAAG,OAAO;AA+BhC,OAAO,SAASC,WAAW,CAACC,SAAiB,EAAE;EAC7C,MAAMC,KAAY,GAAG;IACnBC,GAAG,EAAEL,IAAI,EAAE;IACXM,IAAI,EAAEL,SAAS;IACfM,IAAI,EAAE,EAAE;IACRC,YAAY,EAAE,EAAE;IAChBC,IAAI,EAAE,EAAE;IACRC,IAAI,EAAE,EAAE;IACRC,cAAc,EAAE,CAAC;IACjBC,qBAAqB,EAAE,CAAC;IACxBC,sBAAsB,EAAE,CAAC;IACzBC,+BAA+B,EAAE,CAAC;IAClCC,WAAW,EAAE,CAAC;IACdC,YAAY,EAAE,IAAI;IAClBC,WAAW,EAAE,KAAK;IAClBC,mBAAmB,EAAE,CAAC;IACtBC,uBAAuB,EAAE,CAAC;IAC1BC,kBAAkB,EAAE,CAAC;IACrBC,mBAAmB,EAAE,CAAC;IACtBC,uBAAuB,EAAE;EAC3B,CAAC;EAED,IAAGnB,SAAS,EAAE;IACZoB,MAAM,CAACC,MAAM,CAACpB,KAAK,EAAED,SAAS,CAAC;EACjC;EAEA,OAAOC,KAAK;AACd"}, "metadata": {}, "sourceType": "module"}